"""
AGM_NSGA2项目帕累托前沿数据不一致问题分析

本脚本专门用于分析和诊断帕累托前沿数据在不同阶段的不一致问题
"""

import numpy as np
import matplotlib.pyplot as plt
import copy
from typing import List, Tuple, Dict, Any
import os

def analyze_pareto_front_flow():
    """
    分析帕累托前沿数据流的关键节点
    
    根据代码分析，帕累托前沿数据经过以下关键节点：
    1. ES_global.py: environmental_selection() - 生成帕累托前沿
    2. MARMT_RK_global.py: plot_pareto_front() - 显示帕累托前沿
    3. MARMT_RK_global.py: 保存逻辑 - 保存帕累托前沿
    """
    
    print("=== 帕累托前沿数据流分析 ===")
    
    # 关键数据流节点
    flow_points = [
        {
            "stage": "1. 环境选择生成",
            "location": "ES_global.py:74-78",
            "function": "environmental_selection()",
            "description": "使用DEAP的sortNondominated提取第一个非支配前沿",
            "data_format": "[(g1, g2, constraints), ...]",
            "potential_issues": [
                "DEAP库的非支配排序可能过滤掉某些解",
                "约束违反值的处理可能影响前沿大小",
                "数据类型转换可能导致精度损失"
            ]
        },
        {
            "stage": "2. 显示处理",
            "location": "MARMT_RK_global.py:26-68",
            "function": "plot_pareto_front()",
            "description": "提取前两个目标值进行可视化",
            "data_format": "只使用point[0]和point[1]",
            "potential_issues": [
                "只显示前两个目标值，忽略约束信息",
                "空前沿检查可能跳过某些代",
                "数据提取逻辑可能过滤某些点"
            ]
        },
        {
            "stage": "3. 保存处理",
            "location": "MARMT_RK_global.py:391-395",
            "function": "np.save()",
            "description": "将帕累托前沿保存为numpy数组",
            "data_format": "np.array(pareto_front)",
            "potential_issues": [
                "numpy数组转换可能改变数据结构",
                "保存的是最终代的前沿，不是所有代的合并",
                "数据序列化可能丢失某些信息"
            ]
        }
    ]
    
    for point in flow_points:
        print(f"\n{point['stage']}")
        print(f"位置: {point['location']}")
        print(f"函数: {point['function']}")
        print(f"描述: {point['description']}")
        print(f"数据格式: {point['data_format']}")
        print("潜在问题:")
        for issue in point['potential_issues']:
            print(f"  - {issue}")
    
    return flow_points

def identify_critical_bugs():
    """
    识别可能导致帕累托前沿不一致的关键bug
    """
    
    print("\n=== 关键Bug分析 ===")
    
    critical_bugs = [
        {
            "bug_id": "BUG-001",
            "severity": "HIGH",
            "location": "ES_global.py:77-78",
            "description": "帕累托前沿包含约束违反值",
            "issue": "pareto_front包含3个元素(g1, g2, constraints)，但显示和某些处理只使用前2个",
            "impact": "显示的点数可能与实际保存的点数不一致",
            "evidence": "pareto_front = [(individual.fitness.values[0], individual.fitness.values[1], individual.constraints)]",
            "fix_suggestion": "统一数据格式，或在显示时正确处理约束信息"
        },
        {
            "bug_id": "BUG-002", 
            "severity": "MEDIUM",
            "location": "MARMT_RK_global.py:40-41",
            "description": "显示逻辑假设数据格式",
            "issue": "plot_pareto_front假设每个点只有2个元素，但实际可能有3个",
            "impact": "如果点包含约束信息，可能导致索引错误或数据丢失",
            "evidence": "taxiing_time_values = [point[0] for point in pareto_front]",
            "fix_suggestion": "添加数据格式检查和兼容性处理"
        },
        {
            "bug_id": "BUG-003",
            "severity": "MEDIUM", 
            "location": "ES_global.py:74",
            "description": "非支配排序可能过滤有约束违反的解",
            "issue": "DEAP的sortNondominated可能不正确处理带约束的多目标优化",
            "impact": "某些可行解可能被错误排除",
            "evidence": "tools.sortNondominated(deap_individuals, len(deap_individuals), first_front_only=True)",
            "fix_suggestion": "检查DEAP库对约束处理的行为，可能需要自定义非支配排序"
        },
        {
            "bug_id": "BUG-004",
            "severity": "LOW",
            "location": "MARMT_RK_global.py:36-37",
            "description": "空前沿处理可能掩盖问题",
            "issue": "如果帕累托前沿为空，直接返回而不报告问题",
            "impact": "可能掩盖算法收敛问题或数据处理错误",
            "evidence": "if not pareto_front: return",
            "fix_suggestion": "添加日志记录空前沿的情况"
        }
    ]
    
    for bug in critical_bugs:
        print(f"\n{bug['bug_id']} - 严重程度: {bug['severity']}")
        print(f"位置: {bug['location']}")
        print(f"描述: {bug['description']}")
        print(f"问题: {bug['issue']}")
        print(f"影响: {bug['impact']}")
        print(f"证据: {bug['evidence']}")
        print(f"修复建议: {bug['fix_suggestion']}")
    
    return critical_bugs

def analyze_data_format_inconsistency():
    """
    分析数据格式不一致问题
    """
    
    print("\n=== 数据格式不一致分析 ===")
    
    format_analysis = {
        "ES_global.py生成的格式": {
            "structure": "[(g1, g2, constraints), ...]",
            "elements_per_point": 3,
            "example": "(1234.5, 67.8, 0)",
            "usage": "环境选择返回的帕累托前沿"
        },
        "plot_pareto_front期望的格式": {
            "structure": "[(g1, g2), ...]",
            "elements_per_point": 2,
            "example": "(1234.5, 67.8)",
            "usage": "可视化显示"
        },
        "保存文件的格式": {
            "structure": "np.array(pareto_front)",
            "elements_per_point": "取决于输入",
            "example": "可能是2或3个元素",
            "usage": "持久化存储"
        }
    }
    
    for format_name, details in format_analysis.items():
        print(f"\n{format_name}:")
        print(f"  结构: {details['structure']}")
        print(f"  每点元素数: {details['elements_per_point']}")
        print(f"  示例: {details['example']}")
        print(f"  用途: {details['usage']}")
    
    print("\n不一致的根本原因:")
    print("1. ES_global.py返回包含约束信息的3元组")
    print("2. plot_pareto_front只处理前2个元素")
    print("3. 保存逻辑直接保存原始数据，格式不确定")
    print("4. 没有统一的数据格式标准")
    
    return format_analysis

if __name__ == "__main__":
    print("AGM_NSGA2项目帕累托前沿数据不一致问题深度分析")
    print("=" * 60)
    
    # 分析数据流
    flow_points = analyze_pareto_front_flow()
    
    # 识别关键bug
    critical_bugs = identify_critical_bugs()
    
    # 分析数据格式不一致
    format_analysis = analyze_data_format_inconsistency()
    
    print("\n=== 总结 ===")
    print("发现的主要问题:")
    print("1. 数据格式不一致：生成3元组，显示用2元组")
    print("2. 约束处理不统一：某些地方忽略约束信息")
    print("3. 错误处理不足：空前沿等异常情况处理不当")
    print("4. 数据流缺乏验证：各阶段间没有数据完整性检查")
    
    print("\n建议的修复优先级:")
    print("1. HIGH: 统一帕累托前沿数据格式")
    print("2. MEDIUM: 改进约束处理逻辑")
    print("3. MEDIUM: 添加数据完整性验证")
    print("4. LOW: 改进错误处理和日志记录")
