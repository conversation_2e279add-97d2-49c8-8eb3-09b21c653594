"""
配置模块 - 包含系统配置参数

本模块定义了系统中使用的各种配置参数，包括：
1. 飞机类别参数
2. 路段类型速度配置
3. 算法参数
4. 物理常数
"""

# 飞机类别参数
AIRCRAFT_PARAMETERS = {
    # Learjet 35A light
    'light': {
        'weight': 8300,
        'fuel_flow_7': 0.024,
        'fuel_flow_30': 0.067,
        'F0': 2 * 15.6 * 1000,  # 转换为N
        'mu': 0.015  # 滚动阻力系数
    },
    # Airbus A320 medium
    'Medium': {
        'weight': 78000,
        'fuel_flow_7': 0.101,
        'fuel_flow_30': 0.291,
        'F0': 2 * 111.2 * 1000,
        'mu': 0.015  # 滚动阻力系数
    },
    # Airbus A333 heavy
    'Heavy': {
        'weight': 230000,
        'fuel_flow_7': 0.228,
        'fuel_flow_30': 0.724,
        'F0': 2 * 287 * 1000,
        'mu': 0.015  # 滚动阻力系数
    }
}

# 路段类别表，不同路段类别的起始、结束和最大速度，速度单位为米每秒（m/s）
SEGMENT_TYPE_SPEEDS = {
    'straight breakaway': (0, 5.14, 15.43),  # 起始速度0，结束速度5.14 m/s
    'straight holding': (5.14, 0, 15.43),  # 起始速度5.14，结束速度0 m/s
    'straight': (5.14, 5.14, 15.43),  # 起始速度和结束速度均为5.14 m/s
    'turning': (5.14, 5.14, 5.14)  # 转弯段，恒定速度
}

# 物理常数
PHYSICS_CONSTANTS = {
    'acceleration': 0.98,  # 加速度 (m/s^2)
    'deceleration': 0.98,  # 减速度 (m/s^2)
    'cruise_speed': 5.14,  # 匀速滑行速度 (m/s)
    'turning_speed': 5.14,  # 转弯速度 (m/s)
    'epsilon': 1e-6  # 数值计算容忍误差
}

# 算法参数
ALGORITHM_PARAMETERS = {
    'population_size': 100,
    'max_generations': 100,
    'crossover_probability': 0.6,
    'mutation_probability': 0.1,
    'random_factor_max': 4.5
}

# 并行计算参数
PARALLEL_CONFIG = {
    'enable_parallel': True,
    'max_workers': None,  # None表示使用所有可用CPU核心
    'chunk_size': 10  # 每个进程处理的个体数量
}

# 权重类别映射
WEIGHT_CLASS_MAPPING = {
    '1': 'light',
    '2': 'Medium', 
    '3': 'Heavy'
}
