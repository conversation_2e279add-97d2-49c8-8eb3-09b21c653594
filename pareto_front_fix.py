"""
帕累托前沿数据不一致问题修复实现

本模块提供了修复帕累托前沿数据不一致问题的具体实现
"""

import numpy as np
import matplotlib.pyplot as plt
import os
from typing import List, Tuple, Any, Union
import copy


class ParetoFrontHandler:
    """帕累托前沿数据处理器 - 统一数据格式和处理逻辑"""
    
    @staticmethod
    def normalize_pareto_front(pareto_front: List[Any]) -> List[Tuple[float, float]]:
        """
        标准化帕累托前沿数据格式
        
        参数:
            pareto_front: 原始帕累托前沿数据，可能包含2或3个元素的元组
            
        返回:
            标准化后的帕累托前沿，只包含(g1, g2)目标值
        """
        if not pareto_front:
            return []
        
        normalized_front = []
        
        for point in pareto_front:
            if isinstance(point, (list, tuple)):
                if len(point) >= 2:
                    # 只取前两个目标值
                    g1, g2 = float(point[0]), float(point[1])
                    
                    # 如果有第三个元素（约束），检查是否可行
                    if len(point) >= 3:
                        constraints = point[2]
                        if constraints > 0:
                            continue  # 跳过不可行解
                    
                    normalized_front.append((g1, g2))
                else:
                    print(f"警告: 跳过格式不正确的点: {point}")
            else:
                print(f"警告: 跳过非元组/列表类型的点: {type(point)}")
        
        return normalized_front
    
    @staticmethod
    def validate_pareto_front(pareto_front: List[Tuple[float, float]]) -> bool:
        """
        验证帕累托前沿数据的有效性
        
        参数:
            pareto_front: 标准化后的帕累托前沿
            
        返回:
            是否有效
        """
        if not pareto_front:
            return False
        
        for i, point in enumerate(pareto_front):
            if not isinstance(point, (list, tuple)) or len(point) != 2:
                print(f"错误: 第{i}个点格式不正确: {point}")
                return False
            
            try:
                g1, g2 = float(point[0]), float(point[1])
                if not (np.isfinite(g1) and np.isfinite(g2)):
                    print(f"错误: 第{i}个点包含无效值: {point}")
                    return False
            except (ValueError, TypeError):
                print(f"错误: 第{i}个点无法转换为数值: {point}")
                return False
        
        return True
    
    @staticmethod
    def get_pareto_front_stats(pareto_front: List[Tuple[float, float]]) -> dict:
        """
        获取帕累托前沿的统计信息
        
        参数:
            pareto_front: 标准化后的帕累托前沿
            
        返回:
            统计信息字典
        """
        if not pareto_front:
            return {"size": 0, "valid": False}
        
        g1_values = [point[0] for point in pareto_front]
        g2_values = [point[1] for point in pareto_front]
        
        return {
            "size": len(pareto_front),
            "valid": True,
            "g1_range": (min(g1_values), max(g1_values)),
            "g2_range": (min(g2_values), max(g2_values)),
            "g1_mean": np.mean(g1_values),
            "g2_mean": np.mean(g2_values)
        }


def improved_plot_pareto_front(pareto_front, generation, output_folder, run_index):
    """
    改进的帕累托前沿绘制函数
    
    解决数据格式不一致和错误处理问题
    """
    # 数据预处理和验证
    normalized_front = ParetoFrontHandler.normalize_pareto_front(pareto_front)
    
    if not normalized_front:
        print(f"警告: Generation {generation+1} 帕累托前沿为空或无可行解")
        return
    
    if not ParetoFrontHandler.validate_pareto_front(normalized_front):
        print(f"错误: Generation {generation+1} 帕累托前沿数据无效")
        return
    
    # 获取统计信息
    stats = ParetoFrontHandler.get_pareto_front_stats(normalized_front)
    print(f"Generation {generation+1}: 绘制 {stats['size']} 个帕累托前沿点")
    print(f"  目标函数范围 - 时间: [{stats['g1_range'][0]:.2f}, {stats['g1_range'][1]:.2f}]")
    print(f"  目标函数范围 - 燃油: [{stats['g2_range'][0]:.2f}, {stats['g2_range'][1]:.2f}]")
    
    # 提取目标函数值
    taxiing_time_values = [point[0] for point in normalized_front]
    fuel_consumption_values = [point[1] for point in normalized_front]
    
    # 创建图形
    plt.figure(figsize=(10, 7))
    plt.scatter(
        taxiing_time_values,
        fuel_consumption_values,
        c='blue',
        label=f'Generation {generation+1} ({len(normalized_front)} points)',
        alpha=0.7,
        edgecolors='k',
        s=50
    )
    
    # 添加标签和标题
    plt.xlabel("Total Taxiing Time (seconds)", fontsize=12)
    plt.ylabel("Total Fuel Consumption (kg)", fontsize=12)
    plt.title(f"Pareto Front - Generation {generation+1}", fontsize=14)
    plt.legend(fontsize=10)
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 保存图片
    os.makedirs(output_folder, exist_ok=True)
    pf_image_path = os.path.join(output_folder, f"PF_Run{run_index}_Gen{generation+1}.png")
    
    try:
        plt.savefig(pf_image_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"Generation {generation+1}: Pareto front image saved at {pf_image_path}")
    except Exception as e:
        print(f"错误: 保存帕累托前沿图片失败: {e}")
        plt.close()


def improved_environmental_selection(parent_population, offspring_population, 
                                   parent_fitness, offspring_fitness, population_size):
    """
    改进的环境选择函数
    
    解决约束处理和数据格式问题
    """
    from deap import base, creator, tools
    
    # 确保DEAP类型已创建
    try:
        creator.create("FitnessMulti", base.Fitness, weights=(-1.0, -1.0))
    except RuntimeError:
        pass
    
    try:
        creator.create("Individual", list, fitness=creator.FitnessMulti)
    except RuntimeError:
        pass
    
    # 合并父代和子代
    combined_population = parent_population + offspring_population
    combined_fitness = parent_fitness + offspring_fitness
    
    # 转换为DEAP兼容的个体
    deap_individuals = []
    
    for individual_data, fitness_values in zip(combined_population, combined_fitness):
        deap_individual = creator.Individual(list(individual_data.values()))
        deap_individual.original_data = individual_data
        deap_individual.fitness.values = fitness_values[:2]
        deap_individual.constraints = fitness_values[2] if len(fitness_values) > 2 else 0
        deap_individuals.append(deap_individual)
    
    # 执行NSGA-II选择
    selected_individuals = tools.selNSGA2(deap_individuals, population_size)
    
    # 提取选择后的种群
    new_population = [individual.original_data for individual in selected_individuals]
    new_fitness = [(individual.fitness.values[0], individual.fitness.values[1], individual.constraints)
                  for individual in selected_individuals]
    
    # 改进的帕累托前沿提取
    pareto_individuals = tools.sortNondominated(deap_individuals, len(deap_individuals), first_front_only=True)[0]
    
    # 分离可行解和不可行解
    feasible_pareto = [ind for ind in pareto_individuals if ind.constraints == 0]
    infeasible_pareto = [ind for ind in pareto_individuals if ind.constraints > 0]
    
    if feasible_pareto:
        # 优先使用可行解
        pareto_front = [(individual.fitness.values[0], individual.fitness.values[1])
                       for individual in feasible_pareto]
        pareto_set = [individual.original_data for individual in feasible_pareto]
        print(f"帕累托前沿: {len(feasible_pareto)} 个可行解")
    elif infeasible_pareto:
        # 如果没有可行解，使用约束违反最小的解
        best_infeasible = min(infeasible_pareto, key=lambda x: x.constraints)
        pareto_front = [(best_infeasible.fitness.values[0], best_infeasible.fitness.values[1])]
        pareto_set = [best_infeasible.original_data]
        print(f"警告: 没有可行解，使用约束违反最小的解 (约束值: {best_infeasible.constraints})")
    else:
        # 极端情况：没有任何解
        pareto_front = []
        pareto_set = []
        print("警告: 没有找到任何帕累托前沿解")
    
    return new_population, new_fitness, pareto_front, pareto_set


def save_pareto_front_with_metadata(pareto_front, pareto_set, output_folder, run_index, generation=None):
    """
    保存帕累托前沿数据，包含元数据
    
    参数:
        pareto_front: 帕累托前沿
        pareto_set: 帕累托集
        output_folder: 输出文件夹
        run_index: 运行索引
        generation: 代数（可选，如果是最终结果则为None）
    """
    # 标准化数据
    normalized_front = ParetoFrontHandler.normalize_pareto_front(pareto_front)
    stats = ParetoFrontHandler.get_pareto_front_stats(normalized_front)
    
    # 准备保存数据
    save_data = {
        'pareto_front': np.array(normalized_front),
        'pareto_set': np.array(pareto_set, dtype=object),
        'metadata': {
            'run_index': run_index,
            'generation': generation,
            'front_size': stats['size'],
            'g1_range': stats.get('g1_range', (0, 0)),
            'g2_range': stats.get('g2_range', (0, 0)),
            'data_format_version': '2.0'  # 标记数据格式版本
        }
    }
    
    # 确定文件名
    if generation is not None:
        filename = f"PF_PS_run_{run_index}_gen_{generation}.npz"
    else:
        filename = f"PF_PS_run_{run_index}_final.npz"
    
    filepath = os.path.join(output_folder, filename)
    
    try:
        np.savez(filepath, **save_data)
        print(f"帕累托前沿数据已保存: {filepath}")
        print(f"  前沿大小: {stats['size']}")
        if stats['valid']:
            print(f"  目标值范围: g1=[{stats['g1_range'][0]:.2f}, {stats['g1_range'][1]:.2f}], "
                  f"g2=[{stats['g2_range'][0]:.2f}, {stats['g2_range'][1]:.2f}]")
    except Exception as e:
        print(f"错误: 保存帕累托前沿数据失败: {e}")


if __name__ == "__main__":
    # 测试修复功能
    print("测试帕累托前沿修复功能...")
    
    # 模拟不一致的数据格式
    test_data_3_elements = [(100.0, 20.0, 0), (110.0, 18.0, 0), (120.0, 22.0, 1)]  # 包含约束
    test_data_2_elements = [(100.0, 20.0), (110.0, 18.0)]  # 只有目标值
    test_data_mixed = [(100.0, 20.0), (110.0, 18.0, 0), (120.0, 22.0, 1)]  # 混合格式
    
    handler = ParetoFrontHandler()
    
    print("\n测试1: 3元素数据（包含约束）")
    normalized_1 = handler.normalize_pareto_front(test_data_3_elements)
    print(f"原始: {len(test_data_3_elements)} 个点")
    print(f"标准化后: {len(normalized_1)} 个点")
    print(f"统计信息: {handler.get_pareto_front_stats(normalized_1)}")
    
    print("\n测试2: 2元素数据（只有目标值）")
    normalized_2 = handler.normalize_pareto_front(test_data_2_elements)
    print(f"原始: {len(test_data_2_elements)} 个点")
    print(f"标准化后: {len(normalized_2)} 个点")
    print(f"统计信息: {handler.get_pareto_front_stats(normalized_2)}")
    
    print("\n测试3: 混合格式数据")
    normalized_3 = handler.normalize_pareto_front(test_data_mixed)
    print(f"原始: {len(test_data_mixed)} 个点")
    print(f"标准化后: {len(normalized_3)} 个点")
    print(f"统计信息: {handler.get_pareto_front_stats(normalized_3)}")
    
    print("\n✅ 帕累托前沿修复功能测试完成")
