"""
机场滑行路径多目标优化主程序

本程序使用NSGA-II算法优化机场滑行路径，主要目标是：
1. 最小化总滑行时间
2. 最小化总燃油消耗

程序从doh1.txt文件读取机场布局数据，包括节点、边和飞机信息，
然后使用MARMT_RK_global算法进行多次独立运行，并保存结果。
"""

import pandas as pd
import networkx as nx
import numpy as np
import os
from MARMT_RK_global import MARMT_RK_global


def load_airport_data(file_path):
    """
    从文件加载机场布局数据

    参数:
        file_path: 数据文件路径

    返回:
        nodes_df: 节点数据DataFrame
        edges_df: 边数据DataFrame
        aircraft_df: 飞机数据DataFrame
    """
    # 读取机场布局数据
    with open(file_path, 'r') as file:
        lines = file.readlines()

    # 找到节点、边和飞机数据部分
    node_section_start = lines.index('%SECTION%1%;Nodes;\n') + 1
    edge_section_start = lines.index('%SECTION%2%;Edges;\n') + 1
    aircraft_section_start = lines.index('%SECTION%3%;Aircraft;\n') + 1

    # 解析节点数据
    node_data_lines = lines[node_section_start + 1:edge_section_start - 1]
    node_data = []
    for line in node_data_lines:
        if line.strip() and not line.startswith('%'):
            parts = line.strip().split(';')
            if len(parts) >= 4:
                node_id, x, y, specification = parts[1:5]
                node_data.append([node_id, float(x), float(y), specification])

    nodes_df = pd.DataFrame(node_data, columns=['Node ID', 'X', 'Y', 'Specification'])

    # 解析边数据
    edge_data_lines = lines[edge_section_start + 1:aircraft_section_start - 1]
    edge_data = []
    for line in edge_data_lines:
        if line.strip() and not line.startswith('%'):
            parts = line.strip().split(';')
            if len(parts) >= 6:
                edge_id, start_node, end_node, directed, length, edge_type = parts[1:7]
                # 初始状态下，时间窗为空或可通行
                unavailable_time_windows = []
                edge_data.append([edge_id, start_node, end_node, directed, float(length), edge_type, unavailable_time_windows])

    edges_df = pd.DataFrame(edge_data, columns=['Edge ID', 'Start Node', 'End Node', 'Directed', 'Length', 'Type', 'Unavailable Time Windows'])

    # 解析飞机数据
    aircraft_data_lines = lines[aircraft_section_start + 1:]
    aircraft_data = []
    for line in aircraft_data_lines:
        if line.strip() and not line.startswith('%'):
            parts = line.strip().split(';')
            if len(parts) >= 16:
                aircraft_type = parts[1].strip().strip("'")  # 去掉单引号
                start_node = parts[2].strip().strip("'")  # 去掉单引号
                end_node = parts[3].strip().strip("'")  # 去掉单引号
                start_time_str = parts[4].strip("[]")  # 去掉时间字段的方括号
                start_time_values = start_time_str.split(';')
                start_time = float(start_time_values[0].strip())  # 取第一个时间值
                weight_class = parts[16].strip().strip("'")
                aircraft_data.append([aircraft_type, start_node, end_node, start_time, weight_class])

    aircraft_df = pd.DataFrame(aircraft_data, columns=['Type', 'Start Node', 'End Node', 'Start Time', 'Weight Class'])
    # 对aircraft_df按照Start Time进行升序排序
    aircraft_df = aircraft_df.sort_values(by='Start Time').reset_index(drop=True)

    return nodes_df, edges_df, aircraft_df


def create_airport_graph(edges_df):
    """
    根据边数据创建机场无向图

    参数:
        edges_df: 边数据DataFrame

    返回:
        G: NetworkX无向图对象
    """
    # 创建无向图
    G = nx.Graph()

    # 添加节点和边，并包含时间窗信息
    for _, edge in edges_df.iterrows():
        start_node = edge['Start Node']
        end_node = edge['End Node']
        length = edge['Length']
        time_window = edge['Unavailable Time Windows']
        G.add_edge(start_node, end_node, length=length, unavailable_time_windows=time_window)
        G.add_edge(end_node, start_node, length=length, unavailable_time_windows=time_window)

    return G


def save_data_to_file(data, filename):
    """
    将数据保存为npy文件

    参数:
        data: 要保存的数据
        filename: 文件名
    """
    np.save(filename, np.array(data))


def main():
    """主函数"""
    # 读取机场布局数据
    airport_data_path = 'doh1.txt'
    nodes_df, edges_df, aircraft_df = load_airport_data(airport_data_path)

    # 读取速度配置数据
    speed_profile_path = 'doh1_database.csv'
    speed_profile_df = pd.read_csv(speed_profile_path)

    # 创建机场图
    airport_graph = create_airport_graph(edges_df)

    # 创建输出文件夹（如果不存在）
    output_folder = 'result-V1/'
    os.makedirs(output_folder, exist_ok=True)

    # 独立运行30次算法
    for run_index in range(1, 31):
        print(f"开始第 {run_index} 次运行...")

        # 运行MARMT_RK_global算法
        pareto_front, pareto_set = MARMT_RK_global(
            aircraft_df,
            airport_graph,
            nodes_df,
            edges_df,
            speed_profile_df,
            run_index,
            output_folder
        )

        # 保存当前运行的帕累托前沿和帕累托集
        pf_filename = f"{output_folder}PF_run_{run_index}.npy"
        ps_filename = f"{output_folder}PS_run_{run_index}.npy"

        save_data_to_file(pareto_front, pf_filename)
        save_data_to_file(pareto_set, ps_filename)

        print(f"运行 {run_index} 完成，结果已保存到 {pf_filename} 和 {ps_filename}")

    print("所有 30 次运行已完成！")


if __name__ == "__main__":
    main()
