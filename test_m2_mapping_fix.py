"""
M2值映射修复验证测试

验证M2值从三级配置修正为两级配置后的正确性
"""

import pandas as pd
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from CalFitness_globalV2 import calculate_speed_profile_from_m2, find_segment_config_with_tolerance
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保CalFitness_globalV2.py文件存在且包含所需函数")
    sys.exit(1)


def test_data_file_compatibility():
    """测试与实际数据文件的兼容性"""
    print("=" * 60)
    print("测试与doh1_database.csv数据文件的兼容性")
    print("=" * 60)

    try:
        # 读取实际数据文件
        df = pd.read_csv('doh1_database.csv')

        # 检查speed_profile的取值范围
        unique_profiles = sorted(df['speed_profile'].unique())
        print(f"数据文件中speed_profile的取值: {unique_profiles}")

        # 验证只有1和2
        if set(unique_profiles) == {1, 2}:
            print("✅ 确认：数据文件只包含speed_profile=1和2")
        else:
            print(f"❌ 警告：数据文件包含意外的speed_profile值: {unique_profiles}")
            return False

        # 统计各配置的数量
        value_counts = df['speed_profile'].value_counts().sort_index()
        print(f"配置分布:")
        for profile, count in value_counts.items():
            print(f"  speed_profile={profile}: {count}条记录")

        return True

    except FileNotFoundError:
        print("❌ 错误：找不到doh1_database.csv文件")
        return False
    except Exception as e:
        print(f"❌ 错误：读取数据文件失败 - {e}")
        return False


def test_m2_mapping_correctness():
    """测试M2值映射的正确性"""
    print("\n" + "=" * 60)
    print("测试M2值映射的正确性")
    print("=" * 60)

    test_cases = [
        # (m2_value, expected_profile, description)
        (0.0, 1, "最小值"),
        (0.25, 1, "保守模式中点"),
        (0.49, 1, "保守模式上界"),
        (0.499, 1, "边界值-1"),
        (0.5, 2, "边界值"),
        (0.501, 2, "边界值+1"),
        (0.75, 2, "激进模式中点"),
        (1.0, 2, "最大值"),
    ]

    success_count = 0

    for m2_value, expected_profile, description in test_cases:
        actual_profile = calculate_speed_profile_from_m2(m2_value)

        if actual_profile == expected_profile:
            print(f"✅ M2={m2_value:.3f} -> speed_profile={actual_profile} ({description})")
            success_count += 1
        else:
            print(f"❌ M2={m2_value:.3f} -> speed_profile={actual_profile}, 期望={expected_profile} ({description})")

    print(f"\n映射测试结果: {success_count}/{len(test_cases)} 成功")
    return success_count == len(test_cases)


def test_config_lookup_success():
    """测试配置查找成功率"""
    print("\n" + "=" * 60)
    print("测试配置查找成功率")
    print("=" * 60)

    try:
        # 读取数据文件
        speed_profile_df = pd.read_csv('doh1_database.csv')

        # 测试用例：模拟实际算法中的查找请求
        test_cases = [
            ('Heavy', 'straight', 627.62, 0.25),   # M2=0.25 -> speed_profile=1
            ('Heavy', 'straight', 402.3, 0.75),    # M2=0.75 -> speed_profile=2
            ('Medium', 'straight', 258.3, 0.3),    # M2=0.3 -> speed_profile=1
            ('Medium', 'straight', 444.15, 0.8),   # M2=0.8 -> speed_profile=2
            ('light', 'straight', 150.0, 0.6),     # M2=0.6 -> speed_profile=2
        ]

        success_count = 0

        for weight_type, segment_type, segment_length, m2_value in test_cases:
            # 计算speed_profile
            speed_profile = calculate_speed_profile_from_m2(m2_value)

            print(f"\n测试: {weight_type}, {segment_type}, {segment_length:.2f}")
            print(f"  M2={m2_value:.2f} -> speed_profile={speed_profile}")

            # 查找配置
            try:
                config = find_segment_config_with_tolerance(
                    speed_profile_df, weight_type, segment_type, segment_length, speed_profile
                )

                if not config.empty:
                    matched_length = config['segment_length'].values[0]
                    g1 = config['g1'].values[0]
                    g2 = config['g2'].values[0]
                    print(f"  ✅ 找到配置: 匹配长度={matched_length:.2f}, g1={g1:.2f}, g2={g2:.2f}")
                    success_count += 1
                else:
                    print(f"  ❌ 未找到配置")

            except Exception as e:
                print(f"  ❌ 查找错误: {e}")

        print(f"\n配置查找测试结果: {success_count}/{len(test_cases)} 成功")
        return success_count == len(test_cases)

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_no_profile_3_requests():
    """测试不再有speed_profile=3的请求"""
    print("\n" + "=" * 60)
    print("测试不再有speed_profile=3的请求")
    print("=" * 60)

    # 生成大量随机M2值
    np.random.seed(42)  # 确保可重现
    random_m2_values = np.random.random(1000)

    profile_3_count = 0
    profile_counts = {1: 0, 2: 0}

    for m2_value in random_m2_values:
        profile = calculate_speed_profile_from_m2(m2_value)

        if profile == 3:
            profile_3_count += 1
        elif profile in profile_counts:
            profile_counts[profile] += 1

    print(f"测试1000个随机M2值:")
    print(f"  speed_profile=1: {profile_counts[1]}次")
    print(f"  speed_profile=2: {profile_counts[2]}次")
    print(f"  speed_profile=3: {profile_3_count}次")

    if profile_3_count == 0:
        print("✅ 确认：不再生成speed_profile=3")
        return True
    else:
        print("❌ 错误：仍然生成speed_profile=3")
        return False


def test_distribution_balance():
    """测试分布平衡性"""
    print("\n" + "=" * 60)
    print("测试M2值映射的分布平衡性")
    print("=" * 60)

    # 生成均匀分布的M2值
    m2_values = np.linspace(0, 1, 1000)

    profile_counts = {1: 0, 2: 0}

    for m2_value in m2_values:
        profile = calculate_speed_profile_from_m2(m2_value)
        if profile in profile_counts:
            profile_counts[profile] += 1

    total = sum(profile_counts.values())

    print(f"1000个均匀分布的M2值映射结果:")
    for profile, count in profile_counts.items():
        percentage = (count / total) * 100
        print(f"  speed_profile={profile}: {count}次 ({percentage:.1f}%)")

    # 检查是否接近50-50分布
    profile_1_ratio = profile_counts[1] / total
    expected_ratio = 0.5
    tolerance = 0.05  # 5%容忍度

    if abs(profile_1_ratio - expected_ratio) <= tolerance:
        print(f"✅ 分布平衡良好 (profile=1占比: {profile_1_ratio:.3f})")
        return True
    else:
        print(f"❌ 分布不平衡 (profile=1占比: {profile_1_ratio:.3f}, 期望: {expected_ratio:.3f})")
        return False


def main():
    """主测试函数"""
    print("AGM_NSGA2 M2值映射修复验证测试")
    print("从三级配置修正为两级配置")

    # 运行所有测试
    test_results = []

    test_results.append(test_data_file_compatibility())
    test_results.append(test_m2_mapping_correctness())
    test_results.append(test_config_lookup_success())
    test_results.append(test_no_profile_3_requests())
    test_results.append(test_distribution_balance())

    # 总结结果
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)

    test_names = [
        "数据文件兼容性",
        "M2值映射正确性",
        "配置查找成功率",
        "无speed_profile=3请求",
        "分布平衡性"
    ]

    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")

    overall_success = all(test_results)
    print(f"\n总体结果: {'✅ 所有测试通过' if overall_success else '❌ 部分测试失败'}")

    if overall_success:
        print("\n🎉 M2值映射修复成功！")
        print("✅ 修正为两级配置 (1: 保守/经济模式, 2: 激进/时间模式)")
        print("✅ 与数据文件完全兼容")
        print("✅ 不再出现speed_profile=3查找失败")
    else:
        print("\n⚠️  仍有问题需要解决。")

    return overall_success


if __name__ == "__main__":
    main()
