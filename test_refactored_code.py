"""
测试重构后的代码

本脚本用于验证重构后的代码是否正常工作，包括：
1. 模块导入测试
2. 基本功能测试
3. 性能对比测试
"""

import sys
import time
import traceback
import pandas as pd
import numpy as np

def test_imports():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        # 测试配置模块
        from config import AIRCRAFT_PARAMETERS, SEGMENT_TYPE_SPEEDS, PARALLEL_CONFIG
        print("✓ 配置模块导入成功")
        
        # 测试数据结构模块
        from data_structures import PathSegment, AircraftPath, EvaluationResult
        print("✓ 数据结构模块导入成功")
        
        # 测试路径处理器
        from path_processor import PathProcessor
        print("✓ 路径处理器导入成功")
        
        # 测试段评估器
        from segment_evaluator import SegmentEvaluator
        print("✓ 段评估器导入成功")
        
        # 测试时间窗计算器
        from time_window_calculator import TimeWindowCalculator
        print("✓ 时间窗计算器导入成功")
        
        # 测试适应度评估器
        from fitness_evaluator import FitnessEvaluator
        print("✓ 适应度评估器导入成功")
        
        # 测试重构后的主模块
        import CalFitness_globalV2
        print("✓ 重构后的主模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 模块导入失败: {e}")
        traceback.print_exc()
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n=== 测试基本功能 ===")
    
    try:
        from config import AIRCRAFT_PARAMETERS
        from data_structures import PathSegment, TimeWindow
        
        # 测试配置访问
        assert 'light' in AIRCRAFT_PARAMETERS
        assert 'Medium' in AIRCRAFT_PARAMETERS
        assert 'Heavy' in AIRCRAFT_PARAMETERS
        print("✓ 飞机参数配置正确")
        
        # 测试数据结构
        segment = PathSegment(
            segment_type='straight',
            length=100.0,
            start_node='A',
            end_node='B',
            start_m2=0.0,
            nodes=['A', 'B'],
            edge_lengths=[100.0]
        )
        assert segment.get_total_length() == 100.0
        print("✓ 路径段数据结构正常")
        
        # 测试时间窗
        tw1 = TimeWindow(0.0, 10.0)
        tw2 = TimeWindow(5.0, 15.0)
        assert tw1.overlaps_with(tw2)
        print("✓ 时间窗功能正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_parallel_config():
    """测试并行配置"""
    print("\n=== 测试并行配置 ===")
    
    try:
        from config import PARALLEL_CONFIG
        
        # 检查配置项
        required_keys = ['enable_parallel', 'max_workers', 'chunk_size']
        for key in required_keys:
            assert key in PARALLEL_CONFIG, f"缺少配置项: {key}"
        
        print(f"✓ 并行配置正确: {PARALLEL_CONFIG}")
        return True
        
    except Exception as e:
        print(f"✗ 并行配置测试失败: {e}")
        traceback.print_exc()
        return False

def test_compatibility():
    """测试向后兼容性"""
    print("\n=== 测试向后兼容性 ===")
    
    try:
        import CalFitness_globalV2
        
        # 检查关键函数是否存在
        assert hasattr(CalFitness_globalV2, 'evaluate_path')
        assert hasattr(CalFitness_globalV2, 'evaluate_population')
        assert hasattr(CalFitness_globalV2, 'calculate_angle')
        assert hasattr(CalFitness_globalV2, 'calculate_time_window_conflicts')
        
        print("✓ 向后兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 向后兼容性测试失败: {e}")
        traceback.print_exc()
        return False

def create_mock_data():
    """创建模拟数据用于测试"""
    # 创建简单的节点数据
    nodes_data = {
        'Node ID': ['A', 'B', 'C'],
        'X': [0, 100, 200],
        'Y': [0, 0, 100]
    }
    nodes_df = pd.DataFrame(nodes_data)
    
    # 创建简单的边数据
    edges_data = {
        'Start Node': ['A', 'B'],
        'End Node': ['B', 'C'],
        'Length': [100.0, 141.42]
    }
    edges_df = pd.DataFrame(edges_data)
    
    # 创建简单的速度配置数据
    speed_data = {
        'aircraft_weight_class': ['light', 'light'],
        'segment_type': ['straight', 'turning'],
        'segment_length': [100.0, 141.42],
        'speed_profile': [1, 1],
        'a1': [0.98, 0.0],
        'd1': [10.0, 0.0],
        'd2': [80.0, 141.42],
        'd4': [10.0, 0.0],
        'g1': [25.0, 27.5],
        'g2': [0.6, 0.66]
    }
    speed_df = pd.DataFrame(speed_data)
    
    return nodes_df, edges_df, speed_df

def test_path_processor():
    """测试路径处理器"""
    print("\n=== 测试路径处理器 ===")
    
    try:
        from path_processor import PathProcessor
        
        nodes_df, edges_df, _ = create_mock_data()
        processor = PathProcessor(nodes_df, edges_df)
        
        # 测试角度计算
        edge1 = edges_df.iloc[[0]]
        edge2 = edges_df.iloc[[1]]
        
        angle = processor.calculate_angle(edge1, edge2)
        assert isinstance(angle, (int, float))
        assert 0 <= angle <= 180
        
        print(f"✓ 路径处理器测试通过，计算角度: {angle:.2f}度")
        return True
        
    except Exception as e:
        print(f"✗ 路径处理器测试失败: {e}")
        traceback.print_exc()
        return False

def run_all_tests():
    """运行所有测试"""
    print("开始运行重构代码测试...")
    
    tests = [
        test_imports,
        test_basic_functionality,
        test_parallel_config,
        test_compatibility,
        test_path_processor
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试异常: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！重构成功！")
        return True
    else:
        print("❌ 部分测试失败，需要修复")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
