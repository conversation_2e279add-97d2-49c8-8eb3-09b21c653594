"""
时间窗计算器模块 - 计算路径段的时间窗

本模块包含时间窗计算的核心功能：
1. 基于物理模型的时间计算
2. 边级别的时间窗生成
3. 冲突检测和处理
"""

import math
from typing import List, Tuple, Dict, Any
from data_structures import PathSegment, TimeWindow, EdgeTimeWindow, ConflictInfo
from config import PHYSICS_CONSTANTS
import networkx as nx


class TimeWindowCalculator:
    """时间窗计算器类"""
    
    def __init__(self):
        """初始化时间窗计算器"""
        self.epsilon = PHYSICS_CONSTANTS['epsilon']
        self.deceleration = PHYSICS_CONSTANTS['deceleration']
    
    def calculate_time_for_edges(self, a1: float, d1: float, d2: float, v0: float, v4: float,
                                edges: List[float], current_time: float) -> List[Tuple[float, float]]:
        """
        计算每条边的时间窗
        
        参数:
            a1: 加速度
            d1: 加速距离
            d2: 匀速距离
            v0: 初始速度
            v4: 最终速度
            edges: 边长度列表
            current_time: 当前时间
            
        返回:
            时间窗列表 [(start_time, end_time), ...]
        """
        times = []
        total_distance = 0
        total_time = current_time
        
        # 计算加速阶段末端速度
        v1 = math.sqrt(v0 ** 2 + 2 * a1 * d1) if a1 > 0 else v0
        
        # 计算各阶段时间
        t1 = (v1 - v0) / a1 if a1 > 0 else 0
        t2 = d2 / v1 if v1 > 0 else 0
        
        for edge_length in edges:
            t_start = total_time
            total_distance += edge_length
            
            # 根据总距离判断当前处于哪个阶段
            if total_distance <= d1 + self.epsilon:
                # 加速阶段
                if a1 > 0:
                    t_edge = (math.sqrt(v0 ** 2 + 2 * a1 * total_distance) - v0) / a1
                else:
                    t_edge = total_distance / v0 if v0 > 0 else 0
            elif total_distance <= (d1 + d2 + self.epsilon):
                # 匀速阶段
                t_edge = t1 + (total_distance - d1) / v1 if v1 > 0 else t1
            else:
                # 减速阶段
                remaining_distance = total_distance - (d1 + d2)
                remaining_velocity_squared = v1 ** 2 - 2 * self.deceleration * remaining_distance
                
                if remaining_velocity_squared < 0:
                    remaining_velocity_squared = 0
                
                if remaining_distance > 0:
                    t_edge = t1 + t2 + (v1 - math.sqrt(remaining_velocity_squared)) / self.deceleration
                else:
                    t_edge = t1 + t2
            
            t_edge = t_edge - (t_start - current_time)
            t_end = total_time + t_edge
            
            times.append((t_start, t_end))
            total_time = t_end
        
        return times
    
    def calculate_segment_time_windows(self, segment: PathSegment, physics_params: Dict[str, float],
                                     current_time: float) -> List[EdgeTimeWindow]:
        """
        计算段的时间窗
        
        参数:
            segment: 路径段
            physics_params: 物理参数字典
            current_time: 当前时间
            
        返回:
            边时间窗列表
        """
        edge_time_windows = []
        
        if segment.segment_type == 'turning':
            # 转弯段：匀速通过
            constant_speed = PHYSICS_CONSTANTS['turning_speed']
            edge_times = self.calculate_time_for_edges(
                0, 0, segment.length, constant_speed, constant_speed,
                segment.edge_lengths, current_time
            )
        else:
            # 直线段：使用物理参数
            edge_times = self.calculate_time_for_edges(
                physics_params['a1'], physics_params['d1'], physics_params['d2'],
                physics_params['v0'], physics_params['v4'],
                segment.edge_lengths, current_time
            )
        
        # 创建边时间窗对象
        for i, (t1, t2) in enumerate(edge_times):
            if i < len(segment.nodes) - 1:
                edge_time_window = EdgeTimeWindow(
                    start_node=segment.nodes[i],
                    end_node=segment.nodes[i + 1],
                    time_window=TimeWindow(t1, t2)
                )
                edge_time_windows.append(edge_time_window)
        
        return edge_time_windows
    
    def calculate_time_window_conflicts(self, airport_graph: nx.Graph, 
                                      edge_time_windows: List[EdgeTimeWindow]) -> Tuple[int, List[ConflictInfo]]:
        """
        计算时间窗冲突
        
        参数:
            airport_graph: 机场图
            edge_time_windows: 边时间窗列表
            
        返回:
            (冲突数量, 冲突信息列表)
        """
        conflicts = 0
        conflict_infos = []
        
        for edge_time_window in edge_time_windows:
            edge = edge_time_window.get_edge_tuple()
            time_window = edge_time_window.time_window
            
            if airport_graph.has_edge(edge[0], edge[1]):
                unavailable_windows = airport_graph.edges[edge[0], edge[1]].get('unavailable_time_windows', [])
                
                delay_required = 0
                conflicting_windows = []
                
                for window_start, window_end in unavailable_windows:
                    unavailable_window = TimeWindow(window_start, window_end)
                    
                    # 检查是否有重叠
                    if time_window.overlaps_with(unavailable_window):
                        conflicts += 1
                        dt = window_end - time_window.start_time
                        delay_required += max(0, dt)
                        conflicting_windows.append(unavailable_window)
                
                if delay_required > 0:
                    conflict_info = ConflictInfo(
                        edge=edge,
                        delay_required=delay_required,
                        conflicting_windows=conflicting_windows
                    )
                    conflict_infos.append(conflict_info)
        
        return conflicts, conflict_infos
    
    def merge_time_windows(self, time_windows: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
        """
        合并重叠的时间窗
        
        参数:
            time_windows: 时间窗列表
            
        返回:
            合并后的时间窗列表
        """
        if not time_windows:
            return time_windows
        
        # 按开始时间排序
        sorted_windows = sorted(time_windows, key=lambda x: x[0])
        merged_windows = [sorted_windows[0]]
        
        for current_start, current_end in sorted_windows[1:]:
            last_start, last_end = merged_windows[-1]
            
            # 如果当前窗口与最后一个窗口重叠或相邻，合并它们
            if current_start <= last_end:
                merged_windows[-1] = (last_start, max(last_end, current_end))
            else:
                merged_windows.append((current_start, current_end))
        
        return merged_windows
    
    def update_graph_time_windows(self, airport_graph: nx.Graph, 
                                 edge_time_windows: List[EdgeTimeWindow]) -> None:
        """
        更新图中的时间窗信息
        
        参数:
            airport_graph: 机场图
            edge_time_windows: 边时间窗列表
        """
        for edge_time_window in edge_time_windows:
            start_node = edge_time_window.start_node
            end_node = edge_time_window.end_node
            time_window = edge_time_window.time_window
            
            if airport_graph.has_edge(start_node, end_node):
                # 获取当前的不可用时间窗
                current_windows = airport_graph[start_node][end_node].get('unavailable_time_windows', [])
                
                # 添加新的时间窗
                current_windows.append((time_window.start_time, time_window.end_time))
                
                # 合并重叠的时间窗
                merged_windows = self.merge_time_windows(current_windows)
                
                # 更新图
                airport_graph[start_node][end_node]['unavailable_time_windows'] = merged_windows
                
                # 如果是无向图，也更新反向边
                if airport_graph.has_edge(end_node, start_node):
                    airport_graph[end_node][start_node]['unavailable_time_windows'] = merged_windows
