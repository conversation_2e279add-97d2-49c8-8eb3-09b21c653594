"""
路径处理器模块 - 处理路径解码、修复和段分类

本模块包含路径处理的核心功能：
1. 路径解码和修复
2. 边类型分类（直线/转弯）
3. 路径段合并和处理
4. 路径有效性验证
"""

import math
import numpy as np
from typing import List, Tuple, Dict, Any
from Decoding import decode_individual
from data_structures import PathSegment, AircraftPath
from config import PHYSICS_CONSTANTS
import copy


class PathProcessor:
    """路径处理器类"""
    
    def __init__(self, nodes_df, edges_df):
        """
        初始化路径处理器
        
        参数:
            nodes_df: 节点数据DataFrame
            edges_df: 边数据DataFrame
        """
        self.nodes_df = nodes_df
        self.edges_df = edges_df
    
    def calculate_angle(self, edge1, edge2) -> float:
        """
        计算两条边之间的夹角
        
        参数:
            edge1: 第一条边的DataFrame行
            edge2: 第二条边的DataFrame行
            
        返回:
            角度值（度）
        """
        # 获取边的起点和终点坐标
        start_node1 = edge1['Start Node'].values[0]
        end_node1 = edge1['End Node'].values[0]
        start_node2 = edge2['Start Node'].values[0]
        end_node2 = edge2['End Node'].values[0]

        # 从 nodes_df 中查找每个节点的坐标
        start1 = self.nodes_df.loc[self.nodes_df['Node ID'] == start_node1, ['X', 'Y']].values
        end1 = self.nodes_df.loc[self.nodes_df['Node ID'] == end_node1, ['X', 'Y']].values
        start2 = self.nodes_df.loc[self.nodes_df['Node ID'] == start_node2, ['X', 'Y']].values
        end2 = self.nodes_df.loc[self.nodes_df['Node ID'] == end_node2, ['X', 'Y']].values

        # 确保找到对应的坐标
        if start1.size == 0 or end1.size == 0 or start2.size == 0 or end2.size == 0:
            raise ValueError("One of the edges does not have valid nodes in nodes_df.")

        # 将坐标转换为向量
        vector1 = end1[0] - start1[0]
        vector2 = end2[0] - start2[0]

        # 计算余弦值
        cosine_angle = np.dot(vector1, vector2) / (np.linalg.norm(vector1) * np.linalg.norm(vector2))

        # 将余弦值转换为角度（弧度转换为度）
        angle = np.arccos(np.clip(cosine_angle, -1.0, 1.0)) * (180 / np.pi)
        # 确保角度是锐角
        if angle > 90:
            angle = 180 - angle

        return angle
    
    def repair_path(self, individual_encoding: List, aircraft_subgraph, node_vector: List[str], 
                   start_node: str, end_node: str, min_path_length: Dict[str, int]) -> Tuple[List, List[Tuple[str, float]], int]:
        """
        修复路径编码，确保路径可达
        
        参数:
            individual_encoding: 个体编码
            aircraft_subgraph: 飞机子图
            node_vector: 节点向量
            start_node: 起始节点
            end_node: 终止节点
            min_path_length: 最短路径长度字典
            
        返回:
            (修复后的编码, 解码路径, 最小跳数)
        """
        # 解码路径
        path_with_m2 = decode_individual(
            copy.deepcopy(individual_encoding[1:]), 
            aircraft_subgraph, 
            node_vector, 
            start_node, 
            end_node
        )
        
        last_node = path_with_m2[-1][0]
        min_hop = min_path_length[last_node]
        
        # 如果路径不可达，进行修复
        if min_hop > 0:
            repaired_encoding = copy.deepcopy(individual_encoding)
            
            # 遍历路径中的每个节点，修正编码值
            for node, _ in path_with_m2:
                if node in node_vector:
                    node_position = node_vector.index(node)
                    node_min_hop = min_path_length[node]
                    
                    # 更新编码值
                    current_m1, current_m2 = repaired_encoding[node_position + 1]
                    new_m1 = current_m1 - node_min_hop
                    repaired_encoding[node_position + 1] = (new_m1, current_m2)
            
            # 重新解码修复后的路径
            path_with_m2 = decode_individual(
                repaired_encoding[1:], 
                aircraft_subgraph, 
                node_vector, 
                start_node, 
                end_node
            )
            
            last_node = path_with_m2[-1][0]
            min_hop = min_path_length[last_node]
            
            return repaired_encoding, path_with_m2, min_hop
        
        return individual_encoding, path_with_m2, min_hop
    
    def classify_edges(self, path_with_m2: List[Tuple[str, float]]) -> List[Tuple[str, float, str, str]]:
        """
        对路径中的边进行分类（直线/转弯）
        
        参数:
            path_with_m2: 包含节点和M2值的路径
            
        返回:
            边信息列表 [(edge_type, length, start_node, end_node), ...]
        """
        path_edges = []
        prev_edge = None
        
        for i in range(1, len(path_with_m2)):
            # 查找当前边
            current_edge = self.edges_df.loc[
                ((self.edges_df['Start Node'] == path_with_m2[i - 1][0]) &
                 (self.edges_df['End Node'] == path_with_m2[i][0])) |
                ((self.edges_df['Start Node'] == path_with_m2[i][0]) &
                 (self.edges_df['End Node'] == path_with_m2[i - 1][0]))
            ]
            
            length = current_edge['Length'].values[0]
            
            # 计算夹角并确定边类型
            if i > 1 and prev_edge is not None:
                angle = self.calculate_angle(prev_edge, current_edge)
                edge_type = 'turning' if angle > 30 else 'straight'
            else:
                edge_type = 'straight'  # 第一个边默认为直线
            
            path_edges.append((edge_type, length, path_with_m2[i - 1][0], path_with_m2[i][0]))
            prev_edge = current_edge
        
        return path_edges
    
    def process_terminal_edges(self, path_edges: List[Tuple[str, float, str, str]], 
                              path_with_m2: List[Tuple[str, float]], 
                              true_end_node: str) -> Tuple[List[Tuple[str, float, str, str]], List[Tuple[str, float]], int]:
        """
        处理终端边，确保路径正确结束
        
        参数:
            path_edges: 边信息列表
            path_with_m2: 路径节点信息
            true_end_node: 真实终点
            
        返回:
            (处理后的边列表, 处理后的路径, 额外的跳数)
        """
        additional_hops = 0
        
        # 如果最后一个节点不是真实终点
        if path_with_m2[-1][0] != true_end_node:
            last_edge_type = path_edges[-1][0] if path_edges else 'straight'
            
            if last_edge_type != 'turning':
                # 寻找最后一个转弯边
                last_turning_index = None
                for i in reversed(range(len(path_edges))):
                    if path_edges[i][0] == 'turning':
                        last_turning_index = i
                        break
                
                if last_turning_index is not None:
                    # 删减到最后一个转弯边
                    nodes_removed = len(path_edges) - last_turning_index
                    additional_hops += nodes_removed
                    
                    path_edges = path_edges[:last_turning_index + 1]
                    path_with_m2 = path_with_m2[:last_turning_index + 2]
                else:
                    # 没有转弯边，全部删除
                    additional_hops += len(path_edges)
                    path_edges = []
                    path_with_m2 = []
        else:
            # 如果最后节点是真实终点，设置最后一条边为直线
            if path_edges:
                path_edges[-1] = ('straight', path_edges[-1][1], path_edges[-1][2], path_with_m2[-1][0])
        
        return path_edges, path_with_m2, additional_hops
