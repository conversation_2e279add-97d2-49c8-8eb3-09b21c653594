"""
性能对比测试

本脚本用于测试重构后代码的性能改进，特别是并行化的效果
"""

import time
import pandas as pd
import numpy as np
from config import PARALLEL_CONFIG
import copy

def create_test_data():
    """创建测试数据"""
    # 创建节点数据
    nodes_data = []
    for i in range(20):
        nodes_data.append({
            'Node ID': f'N{i}',
            'X': np.random.uniform(0, 1000),
            'Y': np.random.uniform(0, 1000)
        })
    nodes_df = pd.DataFrame(nodes_data)
    
    # 创建边数据
    edges_data = []
    for i in range(19):
        edges_data.append({
            'Start Node': f'N{i}',
            'End Node': f'N{i+1}',
            'Length': np.random.uniform(50, 200)
        })
    edges_df = pd.DataFrame(edges_data)
    
    # 创建速度配置数据
    speed_data = []
    for weight_class in ['light', 'Medium', 'Heavy']:
        for segment_type in ['straight', 'turning', 'straight breakaway', 'straight holding']:
            for length in [50, 100, 150, 200]:
                speed_data.append({
                    'aircraft_weight_class': weight_class,
                    'segment_type': segment_type,
                    'segment_length': length,
                    'speed_profile': 1,
                    'a1': np.random.uniform(0.5, 1.5),
                    'd1': np.random.uniform(5, 15),
                    'd2': np.random.uniform(30, 100),
                    'd4': np.random.uniform(5, 15),
                    'g1': np.random.uniform(10, 50),
                    'g2': np.random.uniform(0.3, 1.0)
                })
    speed_df = pd.DataFrame(speed_data)
    
    # 创建飞机数据
    aircraft_data = []
    for i in range(5):
        aircraft_data.append({
            'Start Node': f'N{np.random.randint(0, 5)}',
            'End Node': f'N{np.random.randint(15, 20)}',
            'Start Time': np.random.uniform(0, 100),
            'Weight Class': str(np.random.randint(1, 4))
        })
    aircraft_df = pd.DataFrame(aircraft_data)
    aircraft_df.index = range(len(aircraft_df))
    
    return nodes_df, edges_df, speed_df, aircraft_df

def create_mock_population(size=50, num_aircraft=5):
    """创建模拟种群"""
    population = []
    for _ in range(size):
        individual = {}
        for aircraft_id in range(num_aircraft):
            # 创建简单的个体编码
            encoding = [np.random.uniform(0, 10)]  # 时间偏移
            for _ in range(10):  # 假设有10个节点
                encoding.append((np.random.uniform(-2, 2), np.random.uniform(0, 1)))
            individual[aircraft_id] = encoding
        population.append(individual)
    return population

def test_parallel_vs_sequential():
    """测试并行vs串行性能"""
    print("=== 性能对比测试 ===")
    
    # 创建测试数据
    nodes_df, edges_df, speed_df, aircraft_df = create_test_data()
    population = create_mock_population(size=20, num_aircraft=3)
    
    # 模拟其他必要的数据结构
    aircraft_subgraphs = {i: None for i in range(3)}
    node_vectors = {i: [f'N{j}' for j in range(10)] for i in range(3)}
    min_path_lengths = {(i, f'N{j}'): np.random.randint(0, 3) for i in range(3) for j in range(10)}
    maxCost = {'max_time': 1000, 'max_fuel': 100}
    
    # 创建简单的图结构
    import networkx as nx
    G = nx.Graph()
    for i in range(19):
        G.add_edge(f'N{i}', f'N{i+1}')
    
    print(f"测试数据: {len(population)}个个体, {len(aircraft_df)}架飞机")
    
    # 测试串行版本
    print("\n--- 串行评估 ---")
    original_config = copy.deepcopy(PARALLEL_CONFIG)
    PARALLEL_CONFIG['enable_parallel'] = False
    
    try:
        from CalFitness_globalV2 import evaluate_population
        
        start_time = time.time()
        eval_pop, repair_pop = evaluate_population(
            population, aircraft_df, aircraft_subgraphs, node_vectors,
            min_path_lengths, nodes_df, edges_df, speed_df, maxCost, G
        )
        sequential_time = time.time() - start_time
        
        print(f"串行评估时间: {sequential_time:.3f}秒")
        print(f"评估结果数量: {len(eval_pop)}")
        
    except Exception as e:
        print(f"串行评估失败: {e}")
        sequential_time = float('inf')
    
    # 测试并行版本
    print("\n--- 并行评估 ---")
    PARALLEL_CONFIG.update(original_config)
    PARALLEL_CONFIG['enable_parallel'] = True
    PARALLEL_CONFIG['chunk_size'] = 5
    
    try:
        start_time = time.time()
        eval_pop_parallel, repair_pop_parallel = evaluate_population(
            population, aircraft_df, aircraft_subgraphs, node_vectors,
            min_path_lengths, nodes_df, edges_df, speed_df, maxCost, copy.deepcopy(G)
        )
        parallel_time = time.time() - start_time
        
        print(f"并行评估时间: {parallel_time:.3f}秒")
        print(f"评估结果数量: {len(eval_pop_parallel)}")
        
        # 计算加速比
        if sequential_time != float('inf'):
            speedup = sequential_time / parallel_time
            print(f"\n性能提升: {speedup:.2f}x")
            
            if speedup > 1.1:
                print("🚀 并行化带来了显著的性能提升！")
            elif speedup > 0.9:
                print("⚖️ 并行化性能与串行相当")
            else:
                print("⚠️ 并行化可能存在开销问题")
        
    except Exception as e:
        print(f"并行评估失败: {e}")
        parallel_time = float('inf')
    
    # 恢复原始配置
    PARALLEL_CONFIG.update(original_config)

def test_memory_usage():
    """测试内存使用情况"""
    print("\n=== 内存使用测试 ===")
    
    try:
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建大量测试数据
        nodes_df, edges_df, speed_df, aircraft_df = create_test_data()
        large_population = create_mock_population(size=100, num_aircraft=5)
        
        current_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = current_memory - initial_memory
        
        print(f"初始内存使用: {initial_memory:.1f} MB")
        print(f"创建测试数据后: {current_memory:.1f} MB")
        print(f"内存增加: {memory_increase:.1f} MB")
        
        if memory_increase < 100:
            print("✓ 内存使用合理")
        else:
            print("⚠️ 内存使用较高，可能需要优化")
            
    except ImportError:
        print("psutil未安装，跳过内存测试")
    except Exception as e:
        print(f"内存测试失败: {e}")

def test_scalability():
    """测试可扩展性"""
    print("\n=== 可扩展性测试 ===")
    
    population_sizes = [10, 20, 50]
    
    for size in population_sizes:
        print(f"\n测试种群大小: {size}")
        
        try:
            nodes_df, edges_df, speed_df, aircraft_df = create_test_data()
            population = create_mock_population(size=size, num_aircraft=2)
            
            # 简化的评估测试
            start_time = time.time()
            
            # 模拟评估过程
            for individual in population[:5]:  # 只测试前5个个体
                for aircraft_id in range(2):
                    # 模拟计算
                    time.sleep(0.001)  # 1ms的模拟计算时间
            
            elapsed_time = time.time() - start_time
            print(f"处理时间: {elapsed_time:.3f}秒")
            print(f"平均每个个体: {elapsed_time/min(5, size)*1000:.1f}ms")
            
        except Exception as e:
            print(f"可扩展性测试失败: {e}")

if __name__ == "__main__":
    print("开始性能测试...")
    
    test_parallel_vs_sequential()
    test_memory_usage()
    test_scalability()
    
    print("\n🎯 性能测试完成！")
