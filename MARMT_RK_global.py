"""
多目标机场滑行路径规划算法 (MARMT-RK) 全局版本

本模块实现了基于NSGA-II的多目标机场滑行路径规划算法，主要功能包括:
1. 构建每架飞机的可行滑行子图
2. 初始化种群
3. 执行进化算法迭代
4. 可视化和保存帕累托前沿

算法优化两个目标:
- 总滑行时间 (g1)
- 总燃油消耗 (g2)
"""

import networkx as nx
import random
import numpy as np
import os
import matplotlib.pyplot as plt
import copy
from CalFitness_globalV2 import evaluate_population
from operationNSGA2 import crossover_and_mutate
from ES_global import environmental_selection


def plot_pareto_front(pareto_front, generation, output_folder, run_index):
    """
    绘制并保存帕累托前沿图

    参数:
        pareto_front: 帕累托前沿点集
        generation: 当前代数
        output_folder: 输出文件夹路径
        run_index: 运行索引
    """
    if not pareto_front:
        return  # 如果帕累托前沿为空，则跳过

    # 提取目标函数值
    taxiing_time_values = [point[0] for point in pareto_front]  # 第一个目标 (g1: 滑行时间)
    fuel_consumption_values = [point[1] for point in pareto_front]  # 第二个目标 (g2: 燃油消耗)

    # 创建图形
    plt.figure(figsize=(10, 7))
    plt.scatter(
        taxiing_time_values,
        fuel_consumption_values,
        c='blue',
        label=f'Generation {generation+1}',
        alpha=0.7,
        edgecolors='k',
        s=50  # 点的大小
    )

    # 添加标签和标题
    plt.xlabel("Total Taxiing Time (seconds)", fontsize=12)
    plt.ylabel("Total Fuel Consumption (kg)", fontsize=12)
    plt.title(f"Pareto Front - Generation {generation+1}", fontsize=14)
    plt.legend(fontsize=10)
    plt.grid(True, linestyle='--', alpha=0.7)

    # 保存图片
    os.makedirs(output_folder, exist_ok=True)  # 确保输出文件夹存在
    pf_image_path = os.path.join(output_folder, f"PF_Run{run_index}_Gen{generation+1}.png")
    plt.savefig(pf_image_path, dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图像，避免内存占用

    print(f"Generation {generation+1}: Pareto front image saved at {pf_image_path}")


def build_aircraft_subgraph(airport_graph, start_node, end_node, nodes_df):
    """
    构建飞机的可行滑行子图

    通过查找从起点到终点的所有简单路径，构建飞机可用的子图。
    过滤掉包含不允许通过的节点(如跑道或登机口)的路径。

    参数:
        airport_graph: 机场完整图
        start_node: 起始节点
        end_node: 终止节点
        nodes_df: 节点数据DataFrame

    返回:
        aircraft_subgraph: 飞机可用的子图
    """
    # 创建图的副本，避免修改原图
    graph_copy = airport_graph.copy()

    try:
        # 查找起点到终点的所有简单路径
        all_paths = list(nx.all_simple_paths(graph_copy, source=start_node, target=end_node))

        # 获取路径中涉及的所有节点和边
        valid_nodes = set()
        valid_edges = set()

        # 遍历所有路径，过滤无效路径
        for path in all_paths:
            is_valid_path = True
            filtered_nodes = []

            # 检查路径中的每个节点
            for node in path:
                # 获取节点的类型规格
                node_spec = nodes_df.loc[nodes_df['Node ID'] == node, 'Specification'].values[0]

                # 如果节点不是起点或终点，且是跑道或登机口，则路径无效
                if (node != start_node and node != end_node and
                    node_spec in ['runway', 'gate']):
                    is_valid_path = False
                    break
                else:
                    filtered_nodes.append(node)

            # 如果路径有效，添加其节点和边到有效集合
            if is_valid_path:
                valid_nodes.update(filtered_nodes)
                # 添加路径中的边
                for i in range(len(filtered_nodes) - 1):
                    valid_edges.add((filtered_nodes[i], filtered_nodes[i + 1]))

        # 创建新的子图
        subgraph = nx.Graph()

        # 添加有效节点和边
        subgraph.add_nodes_from(valid_nodes)
        subgraph.add_edges_from(valid_edges)

        # 如果子图为空或不包含起点和终点，抛出异常
        if not subgraph.has_node(start_node) or not subgraph.has_node(end_node):
            raise nx.NetworkXNoPath(f"No valid path from {start_node} to {end_node}")

        return subgraph

    except Exception as e:
        print(f"Error building subgraph from {start_node} to {end_node}: {e}")
        # 创建一个最小子图，只包含起点和终点
        minimal_subgraph = nx.Graph()
        minimal_subgraph.add_nodes_from([start_node, end_node])
        return minimal_subgraph


def initialize_population(node_vectors, min_path_lengths, population_size, random_factor):
    """
    初始化种群

    为每架飞机创建初始路径编码，包括:
    - 滑行起始时间 (初始为0)
    - 每个节点的M1值 (影响路径选择)
    - 每个节点的M2值 (影响速度选择)

    参数:
        node_vectors: 每架飞机的节点向量
        min_path_lengths: 每个节点到终点的最短路径长度
        population_size: 种群大小
        random_factor: 随机因子，用于M1值的计算

    返回:
        initial_population: 初始种群
    """
    initial_population = []

    # 生成指定大小的种群
    for _ in range(population_size):
        individual = {}

        # 为每架飞机创建路径编码
        for aircraft_id, node_vector in node_vectors.items():
            # 初始化飞机的路径编码，第一个元素为滑行起始时间(初始为0)
            aircraft_path = [0]

            # 为每个节点生成(M1,M2)编码对
            for node in node_vector:
                # M1值影响路径选择，基于到终点的最短路径长度加上随机扰动
                m1_value = -min_path_lengths[(aircraft_id, node)] + random.uniform(0, random_factor)

                # M2值影响速度选择，在[0,1]范围内随机生成
                m2_value = random.uniform(0, 1)

                # 添加节点的编码对
                aircraft_path.append((m1_value, m2_value))

            # 将飞机的路径编码添加到个体中
            individual[aircraft_id] = aircraft_path

        # 将个体添加到种群
        initial_population.append(individual)

    return initial_population


def MARMT_RK_global(aircraft_df, airport_graph, nodes_df, edges_df, speed_profile_df, run_index, output_folder):
    """
    多目标机场滑行路径规划算法主函数

    执行基于NSGA-II的多目标优化算法，寻找最优的滑行路径集合。

    参数:
        aircraft_df: 飞机数据DataFrame
        airport_graph: 机场图
        nodes_df: 节点数据DataFrame
        edges_df: 边数据DataFrame
        speed_profile_df: 速度配置数据DataFrame
        run_index: 运行索引
        output_folder: 输出文件夹路径

    返回:
        pareto_front: 最终帕累托前沿
        pareto_set: 最终帕累托集
    """
    # 添加飞机ID列
    aircraft_df['Aircraft ID'] = aircraft_df.index

    print(f"开始为{len(aircraft_df)}架飞机构建子图...")

    # 为每架飞机构建可行滑行子图
    aircraft_subgraphs = {}
    for _, aircraft in aircraft_df.iterrows():
        aircraft_id = aircraft['Aircraft ID']
        start_node = aircraft['Start Node']
        end_node = aircraft['End Node']

        # 构建子图
        subgraph = build_aircraft_subgraph(
            airport_graph,
            start_node,
            end_node,
            nodes_df
        )
        aircraft_subgraphs[aircraft_id] = subgraph

    print("子图构建完成，准备提取节点向量和计算最短路径...")

    # 初始化节点向量和最短路径长度字典
    node_vectors = {}
    min_path_lengths = {}

    # 为每架飞机提取节点向量并计算最短路径长度
    for _, aircraft in aircraft_df.iterrows():
        aircraft_id = aircraft['Aircraft ID']
        start_node = aircraft['Start Node']
        end_node = aircraft['End Node']
        subgraph = aircraft_subgraphs[aircraft_id]

        # 提取所有节点
        all_nodes = list(subgraph.nodes)

        # 构建节点向量：起点 + 中间节点 + 终点
        node_vector = [start_node]
        node_vector.extend([node for node in all_nodes
                           if node != start_node and node != end_node])
        node_vector.append(end_node)

        # 存储节点向量
        node_vectors[aircraft_id] = node_vector

        # 计算每个节点到终点的最短路径长度
        for node in subgraph.nodes:
            try:
                # 使用NetworkX计算最短路径长度
                path_length = nx.shortest_path_length(subgraph, source=node, target=end_node)
                min_path_lengths[(aircraft_id, node)] = path_length
            except nx.NetworkXNoPath:
                # 如果没有路径，设置为无穷大
                min_path_lengths[(aircraft_id, node)] = float('inf')

    print("节点向量和最短路径计算完成，设置算法参数...")

    # 算法参数设置
    popsize = 100  # 种群大小
    Max_generation = 100  # 最大代数
    rtmax = 4.5  # 随机因子上限
    pc = 0.6  # 交叉概率
    pm = 0.1  # 变异概率

    # 计算最大惩罚成本
    # 假设最长路径长度为200
    longest_path_length = 200

    # 速度和加速度参数
    cruise_speed = 5.14  # 匀速滑行速度 (m/s)
    acceleration = 0.98  # 加速度 (m/s^2)

    # 计算各阶段时间和距离
    accel_time = cruise_speed / acceleration  # 加速时间
    accel_distance = (0 + cruise_speed) / 2 * accel_time  # 加速阶段距离

    decel_time = cruise_speed / acceleration  # 减速时间
    decel_distance = (cruise_speed / 2) * decel_time  # 减速阶段距离

    # 计算匀速阶段
    cruise_distance = longest_path_length - (accel_distance + decel_distance)
    cruise_time = cruise_distance / cruise_speed

    # 计算总滑行时间和燃油消耗
    total_max_time = accel_time + cruise_time + decel_time
    max_fuel_flow = 0.724  # 最大燃油流量 (kg/s)
    total_max_fuel = total_max_time * max_fuel_flow

    # 创建最大成本字典
    max_Cost = {
        'max_time': round(total_max_time, 2),
        'max_fuel': round(total_max_fuel, 2)
    }

    print(f"算法参数设置完成，最大惩罚时间: {max_Cost['max_time']}秒，最大惩罚燃油: {max_Cost['max_fuel']}kg")

    print("初始化种群...")
    # 初始化种群
    population = initialize_population(node_vectors, min_path_lengths, popsize, rtmax)

    # 存储每代的帕累托前沿和帕累托集
    all_PF = []
    all_PS = []

    print("评估初始种群...")
    # 对初始种群进行评价
    evaluated_population, repair_population = evaluate_population(
        copy.deepcopy(population),
        aircraft_df,
        aircraft_subgraphs,  # 使用正确的变量名
        node_vectors,
        min_path_lengths,
        nodes_df,
        edges_df,
        speed_profile_df,
        max_Cost,
        copy.deepcopy(airport_graph)  # 使用正确的变量名
    )
    population = copy.deepcopy(repair_population)

    # 迭代进行进化
    for generation in range(Max_generation):
        print(f"Generation {generation + 1}/{Max_generation}")

        # 进行交叉和变异
        offsprings_population = crossover_and_mutate(
            copy.deepcopy(population),
            pc,  # 交叉概率
            pm   # 变异概率
        )

        # 子代种群评价
        evaluated_offsprings, repair_offsprings_population = evaluate_population(
            copy.deepcopy(offsprings_population),
            aircraft_df,
            aircraft_subgraphs,  # 使用正确的变量名
            node_vectors,
            copy.deepcopy(min_path_lengths),
            nodes_df,
            edges_df,
            speed_profile_df,
            max_Cost,
            copy.deepcopy(airport_graph)  # 使用正确的变量名
        )
        offsprings_population = copy.deepcopy(repair_offsprings_population)

        # 环境选择 - 使用NSGA-II选择下一代种群
        population, evaluated_population, pareto_front, pareto_set = environmental_selection(
            copy.deepcopy(population),          # 父代种群
            copy.deepcopy(offsprings_population), # 子代种群
            copy.deepcopy(evaluated_population),  # 父代适应度
            copy.deepcopy(evaluated_offsprings),  # 子代适应度
            popsize                               # 种群大小
        )

        # 存储当前代的帕累托前沿和帕累托集
        all_PF.append(pareto_front)
        all_PS.append(pareto_set)

        # 绘制并保存当前代的帕累托前沿图
        plot_pareto_front(pareto_front, generation, output_folder, run_index)

        # 输出当前代的目标函数值范围
        if pareto_front:
            min_time = min(point[0] for point in pareto_front)
            max_time = max(point[0] for point in pareto_front)
            min_fuel = min(point[1] for point in pareto_front)
            max_fuel = max(point[1] for point in pareto_front)
            print(f"  目标函数范围 - 时间: [{min_time:.2f}, {max_time:.2f}], 燃油: [{min_fuel:.2f}, {max_fuel:.2f}]")
            print(f"  帕累托前沿大小: {len(pareto_front)}")

    # 创建输出文件夹（如果不存在）
    os.makedirs(output_folder, exist_ok=True)

    # 保存所有代的帕累托前沿和帕累托集
    output_file = os.path.join(output_folder, f"PF_PS_run_{run_index}.npz")
    np.savez(output_file, PF=np.array(all_PF, dtype=object), PS=np.array(all_PS, dtype=object))

    # 保存最终代的帕累托前沿和帕累托集为单独的文件
    final_pf_filename = os.path.join(output_folder, f"PF_run_{run_index}.npy")
    final_ps_filename = os.path.join(output_folder, f"PS_run_{run_index}.npy")
    np.save(final_pf_filename, np.array(pareto_front))
    np.save(final_ps_filename, np.array(pareto_set))

    print(f"运行 {run_index} 完成！")
    print(f"结果已保存到: {output_file}")
    print(f"最终帕累托前沿: {final_pf_filename}")
    print(f"最终帕累托集: {final_ps_filename}")

    # 返回最终的帕累托前沿和帕累托集
    return pareto_front, pareto_set
