"""
环境选择模块 - 实现NSGA-II算法的环境选择操作

本模块使用DEAP库实现NSGA-II算法的环境选择操作，包括:
1. 合并父代和子代种群
2. 基于非支配排序和拥挤度距离进行选择
3. 提取帕累托前沿和帕累托集
"""

from deap import base, creator, tools


def constraint_aware_nsga2_selection(individuals, k):
    """
    约束感知的NSGA-II选择算法

    优先选择可行解，对于不可行解按约束违反程度排序

    参数:
        individuals: 个体列表
        k: 选择的个体数量

    返回:
        选择后的个体列表
    """
    # 分离可行解和不可行解
    feasible = [ind for ind in individuals if ind.constraints == 0]
    infeasible = [ind for ind in individuals if ind.constraints > 0]

    selected = []

    # 优先从可行解中选择
    if feasible:
        if len(feasible) >= k:
            # 可行解足够，直接使用NSGA-II选择
            selected = tools.selNSGA2(feasible, k)
        else:
            # 可行解不足，全部选择
            selected.extend(feasible)
            remaining = k - len(feasible)

            # 从不可行解中选择剩余个体
            if infeasible and remaining > 0:
                # 按约束违反程度排序
                infeasible.sort(key=lambda x: x.constraints)
                selected.extend(infeasible[:remaining])
    else:
        # 没有可行解，按约束违反程度选择
        if infeasible:
            infeasible.sort(key=lambda x: x.constraints)
            selected = infeasible[:k]

    return selected


# 创建DEAP适应度和个体类型（避免重复创建）
try:
    creator.create("FitnessMulti", base.Fitness, weights=(-1.0, -1.0))
except RuntimeError:
    pass  # 如果已经创建，则跳过

try:
    creator.create("Individual", list, fitness=creator.FitnessMulti)
except RuntimeError:
    pass  # 如果已经创建，则跳过


def environmental_selection(parent_population, offspring_population, parent_fitness, offspring_fitness, population_size):
    """
    执行NSGA-II环境选择操作

    将父代和子代合并，然后基于非支配排序和拥挤度距离选择下一代种群。
    同时提取当前种群的帕累托前沿和帕累托集。

    参数:
        parent_population: 父代种群
        offspring_population: 子代种群
        parent_fitness: 父代适应度值
        offspring_fitness: 子代适应度值
        population_size: 种群大小

    返回:
        new_population: 选择后的新种群
        new_fitness: 新种群的适应度值
        pareto_front: 帕累托前沿（目标空间）
        pareto_set: 帕累托集（决策空间）
    """
    # 合并父代和子代
    combined_population = parent_population + offspring_population
    combined_fitness = parent_fitness + offspring_fitness

    # 转换为DEAP兼容的个体
    deap_individuals = []

    for individual_data, fitness_values in zip(combined_population, combined_fitness):
        # 创建DEAP个体（使用列表表示）
        deap_individual = creator.Individual(list(individual_data.values()))

        # 存储原始字典数据，以便后续还原
        deap_individual.original_data = individual_data

        # 设置适应度值（前两个值是目标函数值，第三个是约束违反值）
        deap_individual.fitness.values = fitness_values[:2]
        deap_individual.constraints = fitness_values[2]

        deap_individuals.append(deap_individual)

    # 修复：执行约束感知的NSGA-II选择
    selected_individuals = constraint_aware_nsga2_selection(deap_individuals, population_size)

    # 提取选择后的种群
    new_population = [individual.original_data for individual in selected_individuals]
    new_fitness = [(individual.fitness.values[0], individual.fitness.values[1], individual.constraints)
                  for individual in selected_individuals]

    # 修复：从选择后的种群中识别帕累托前沿
    pareto_individuals = tools.sortNondominated(selected_individuals, len(selected_individuals), first_front_only=True)[0]

    # 提取帕累托前沿和帕累托集
    pareto_front = [(individual.fitness.values[0], individual.fitness.values[1], individual.constraints)
                    for individual in pareto_individuals]
    pareto_set = [individual.original_data for individual in pareto_individuals]

    return new_population, new_fitness, pareto_front, pareto_set
