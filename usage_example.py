"""
重构后代码使用示例

本文件展示了如何使用重构后的AGM_NSGA2代码，包括：
1. 基本使用方法（与原始代码兼容）
2. 新功能使用方法
3. 并行计算配置
4. 性能优化建议
"""

import pandas as pd
import numpy as np
import time
from config import PARALLEL_CONFIG

def basic_usage_example():
    """基本使用示例（与原始代码完全兼容）"""
    print("=== 基本使用示例 ===")
    
    # 导入重构后的模块（接口与原始代码相同）
    from CalFitness_globalV2 import evaluate_path, evaluate_population
    
    print("✓ 重构后的模块导入成功")
    print("✓ 所有原始函数接口保持不变")
    print("✓ 可以直接替换原始代码使用")

def parallel_configuration_example():
    """并行计算配置示例"""
    print("\n=== 并行计算配置示例 ===")
    
    # 查看当前配置
    print(f"当前并行配置: {PARALLEL_CONFIG}")
    
    # 启用并行计算（推荐用于大种群）
    PARALLEL_CONFIG['enable_parallel'] = True
    PARALLEL_CONFIG['max_workers'] = 4  # 使用4个进程
    PARALLEL_CONFIG['chunk_size'] = 20  # 每个进程处理20个个体
    print("✓ 已启用并行计算")
    
    # 禁用并行计算（用于小种群或调试）
    PARALLEL_CONFIG['enable_parallel'] = False
    print("✓ 已禁用并行计算")
    
    # 自动配置（推荐）
    PARALLEL_CONFIG['enable_parallel'] = True
    PARALLEL_CONFIG['max_workers'] = None  # 自动检测CPU核心数
    PARALLEL_CONFIG['chunk_size'] = 10     # 合理的默认值
    print("✓ 已设置为自动配置")

def new_features_example():
    """新功能使用示例"""
    print("\n=== 新功能使用示例 ===")
    
    try:
        # 使用新的模块化组件
        from fitness_evaluator import FitnessEvaluator
        from data_structures import PathSegment, TimeWindow, EvaluationResult
        from path_processor import PathProcessor
        
        print("✓ 新的模块化组件可用")
        
        # 创建路径段示例
        segment = PathSegment(
            segment_type='straight',
            length=100.0,
            start_node='A',
            end_node='B',
            start_m2=0.0,
            nodes=['A', 'B'],
            edge_lengths=[100.0]
        )
        print(f"✓ 路径段创建成功，长度: {segment.get_total_length()}m")
        
        # 创建时间窗示例
        tw1 = TimeWindow(0.0, 10.0)
        tw2 = TimeWindow(5.0, 15.0)
        print(f"✓ 时间窗重叠检测: {tw1.overlaps_with(tw2)}")
        
    except Exception as e:
        print(f"✗ 新功能示例失败: {e}")

def performance_optimization_tips():
    """性能优化建议"""
    print("\n=== 性能优化建议 ===")
    
    print("1. 并行计算配置:")
    print("   - 种群大小 > 50: 启用并行计算")
    print("   - 种群大小 < 20: 使用串行计算")
    print("   - chunk_size = 种群大小 / CPU核心数")
    
    print("\n2. 内存优化:")
    print("   - 及时清理不需要的大型数据结构")
    print("   - 避免深拷贝大型对象")
    print("   - 使用生成器处理大量数据")
    
    print("\n3. 算法优化:")
    print("   - 利用物理参数缓存")
    print("   - 批量处理相似计算")
    print("   - 避免重复的路径解码")

def migration_guide():
    """迁移指南"""
    print("\n=== 迁移指南 ===")
    
    print("从原始代码迁移到重构代码:")
    print()
    print("1. 直接替换导入:")
    print("   # 原始代码")
    print("   # from CalFitness_globalV2 import evaluate_path")
    print("   ")
    print("   # 重构后代码（完全兼容）")
    print("   from CalFitness_globalV2 import evaluate_path")
    print()
    
    print("2. 可选：启用并行计算")
    print("   from config import PARALLEL_CONFIG")
    print("   PARALLEL_CONFIG['enable_parallel'] = True")
    print()
    
    print("3. 可选：使用新的详细结果")
    print("   from fitness_evaluator import FitnessEvaluator")
    print("   evaluator = FitnessEvaluator(...)")
    print("   result = evaluator.evaluate_aircraft_path(...)")
    print("   print(f'约束违反: {result.get_total_constraints()}')")

def benchmark_example():
    """性能基准测试示例"""
    print("\n=== 性能基准测试示例 ===")
    
    # 模拟不同配置下的性能测试
    configs = [
        {'enable_parallel': False, 'description': '串行计算'},
        {'enable_parallel': True, 'chunk_size': 5, 'description': '并行计算(小块)'},
        {'enable_parallel': True, 'chunk_size': 20, 'description': '并行计算(大块)'},
    ]
    
    print("建议的性能测试流程:")
    for i, config in enumerate(configs, 1):
        print(f"{i}. {config['description']}")
        print(f"   配置: {config}")
        print(f"   适用场景: {'调试和小规模测试' if not config['enable_parallel'] else '生产环境大规模计算'}")
        print()

def error_handling_example():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    print("重构后的代码提供了更好的错误处理:")
    print()
    print("1. 数据验证错误:")
    print("   - PathSegment会验证节点和边的一致性")
    print("   - 提供清晰的错误信息")
    print()
    print("2. 配置错误:")
    print("   - 自动检测无效的并行配置")
    print("   - 回退到安全的默认配置")
    print()
    print("3. 计算错误:")
    print("   - 捕获数值计算异常")
    print("   - 提供详细的错误上下文")

def main():
    """主函数"""
    print("AGM_NSGA2 重构后代码使用示例")
    print("=" * 50)
    
    # 运行所有示例
    basic_usage_example()
    parallel_configuration_example()
    new_features_example()
    performance_optimization_tips()
    migration_guide()
    benchmark_example()
    error_handling_example()
    
    print("\n" + "=" * 50)
    print("🎉 示例演示完成！")
    print()
    print("重要提示:")
    print("1. 重构后的代码完全兼容原始接口")
    print("2. 新增的并行计算功能可显著提升大规模计算性能")
    print("3. 模块化设计便于未来扩展和维护")
    print("4. 详细的文档和类型注解提升开发体验")
    print()
    print("开始使用重构后的代码吧！🚀")

if __name__ == "__main__":
    main()
