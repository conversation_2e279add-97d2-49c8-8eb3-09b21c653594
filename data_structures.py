"""
数据结构模块 - 定义系统中使用的数据类

本模块定义了系统中使用的各种数据结构，包括：
1. 路径段 (PathSegment)
2. 飞机路径 (AircraftPath)
3. 评估结果 (EvaluationResult)
4. 时间窗 (TimeWindow)
"""

from dataclasses import dataclass
from typing import List, Tuple, Optional, Dict, Any
import numpy as np


@dataclass
class TimeWindow:
    """时间窗数据结构"""
    start_time: float
    end_time: float

    def overlaps_with(self, other: 'TimeWindow') -> bool:
        """检查是否与另一个时间窗重叠"""
        return not (self.end_time < other.start_time or self.start_time > other.end_time)

    def merge_with(self, other: 'TimeWindow') -> 'TimeWindow':
        """与另一个时间窗合并"""
        return TimeWindow(
            min(self.start_time, other.start_time),
            max(self.end_time, other.end_time)
        )


@dataclass
class PathSegment:
    """路径段数据结构"""
    segment_type: str  # 'straight', 'straight breakaway', 'straight holding', 'turning'
    length: float
    start_node: str
    end_node: str
    start_m2: float  # M2值
    nodes: List[str]  # 包含的所有节点
    edge_lengths: List[float]  # 每条边的长度

    def __post_init__(self):
        """验证数据完整性"""
        if len(self.nodes) != len(self.edge_lengths) + 1:
            raise ValueError("节点数量应该比边数量多1")
        if self.length <= 0:
            raise ValueError("段长度必须大于0")

    def get_total_length(self) -> float:
        """获取段的总长度"""
        return self.length

    def get_edge_count(self) -> int:
        """获取边数量"""
        return len(self.edge_lengths)


@dataclass
class AircraftPath:
    """飞机路径数据结构"""
    aircraft_id: int
    start_node: str
    end_node: str
    start_time: float
    weight_class: str
    path_with_m2: List[Tuple[str, float]]  # [(node, m2_value), ...]
    segments: List[PathSegment]

    def get_total_length(self) -> float:
        """获取路径总长度"""
        return sum(segment.length for segment in self.segments)

    def get_segment_count(self) -> int:
        """获取段数量"""
        return len(self.segments)


@dataclass
class EdgeTimeWindow:
    """边时间窗数据结构"""
    start_node: str
    end_node: str
    time_window: TimeWindow

    def get_edge_tuple(self) -> Tuple[str, str]:
        """获取边的元组表示"""
        return (self.start_node, self.end_node)


@dataclass
class ConflictInfo:
    """冲突信息数据结构"""
    edge: Tuple[str, str]
    delay_required: float
    conflicting_windows: List[TimeWindow]


@dataclass
class EvaluationResult:
    """评估结果数据结构"""
    aircraft_id: int
    total_g1: float  # 总滑行时间
    total_g2: float  # 总燃油消耗
    edge_time_windows: List[EdgeTimeWindow]
    constraint1: int  # 路径约束违反
    constraint2: int  # 时间窗约束违反
    conflicts: List[ConflictInfo]
    repaired_individual: Any  # 修复后的个体编码

    def has_constraints(self) -> bool:
        """是否有约束违反"""
        return self.constraint1 > 0 or self.constraint2 > 0

    def get_total_constraints(self) -> int:
        """获取总约束违反数"""
        return self.constraint1 + self.constraint2


@dataclass
class PopulationEvaluationResult:
    """种群评估结果数据结构"""
    evaluated_population: List[Tuple[float, float, int]]  # [(g1, g2, constraints), ...]
    repaired_population: List[Dict[int, Any]]  # 修复后的种群
    evaluation_details: List[EvaluationResult]  # 详细评估结果

    def get_fitness_values(self) -> np.ndarray:
        """获取适应度值矩阵"""
        return np.array(self.evaluated_population)

    def get_objective_values(self) -> np.ndarray:
        """获取目标函数值矩阵（不包括约束）"""
        return np.array([(g1, g2) for g1, g2, _ in self.evaluated_population])


@dataclass
class SegmentEvaluationParams:
    """段评估参数"""
    segment_type: str
    segment_length: float
    weight_type: str
    speed_profile: int = 1

    def __hash__(self):
        """使参数可哈希，用于缓存"""
        return hash((self.segment_type, self.segment_length, self.weight_type, self.speed_profile))


@dataclass
class PhysicsParams:
    """物理参数数据结构"""
    a1: float  # 加速度
    d1: float  # 加速距离
    d2: float  # 匀速距离
    d4: float  # 减速距离
    g1: float  # 时间
    g2: float  # 燃油消耗
    v0: float  # 初始速度
    v4: float  # 最终速度
