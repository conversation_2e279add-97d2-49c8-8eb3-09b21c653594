"""
配置验证和修复工具

本模块用于验证和修复速度配置数据库的完整性，确保所有可能的组合都有对应的配置。
"""

import pandas as pd
import numpy as np
from typing import List, Tuple, Dict, Set
from config import AIRCRAFT_PARAMETERS, SEGMENT_TYPE_SPEEDS
import warnings


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self, speed_profile_df: pd.DataFrame):
        """
        初始化配置验证器
        
        参数:
            speed_profile_df: 速度配置数据DataFrame
        """
        self.speed_profile_df = speed_profile_df.copy()
        self.missing_configs = []
        self.validation_report = {}
    
    def validate_completeness(self) -> Dict:
        """
        验证配置完整性
        
        返回:
            验证报告字典
        """
        print("开始验证配置完整性...")
        
        # 获取所有可能的组合
        weight_classes = list(AIRCRAFT_PARAMETERS.keys())
        segment_types = list(SEGMENT_TYPE_SPEEDS.keys())
        
        # 从现有数据中获取长度范围
        existing_lengths = sorted(self.speed_profile_df['segment_length'].unique())
        speed_profiles = sorted(self.speed_profile_df['speed_profile'].unique())
        
        total_combinations = len(weight_classes) * len(segment_types) * len(existing_lengths) * len(speed_profiles)
        missing_count = 0
        
        print(f"预期组合数: {total_combinations}")
        print(f"权重类别: {weight_classes}")
        print(f"段类型: {segment_types}")
        print(f"长度范围: {min(existing_lengths):.1f} - {max(existing_lengths):.1f}米 ({len(existing_lengths)}个值)")
        print(f"速度配置: {speed_profiles}")
        
        # 检查每个组合
        for weight_class in weight_classes:
            for segment_type in segment_types:
                for length in existing_lengths:
                    for speed_profile in speed_profiles:
                        if not self._config_exists(weight_class, segment_type, length, speed_profile):
                            self.missing_configs.append({
                                'weight_class': weight_class,
                                'segment_type': segment_type,
                                'segment_length': length,
                                'speed_profile': speed_profile
                            })
                            missing_count += 1
        
        # 生成报告
        self.validation_report = {
            'total_combinations': total_combinations,
            'existing_combinations': len(self.speed_profile_df),
            'missing_combinations': missing_count,
            'completeness_ratio': (total_combinations - missing_count) / total_combinations,
            'missing_configs': self.missing_configs[:10]  # 只显示前10个缺失配置
        }
        
        print(f"\n验证结果:")
        print(f"现有配置: {self.validation_report['existing_combinations']}")
        print(f"缺失配置: {missing_count}")
        print(f"完整性: {self.validation_report['completeness_ratio']:.1%}")
        
        return self.validation_report
    
    def _config_exists(self, weight_class: str, segment_type: str, length: float, speed_profile: int) -> bool:
        """检查配置是否存在"""
        matches = self.speed_profile_df[
            (self.speed_profile_df['aircraft_weight_class'] == weight_class) &
            (self.speed_profile_df['segment_type'] == segment_type) &
            (np.isclose(self.speed_profile_df['segment_length'], length, atol=0.01)) &
            (self.speed_profile_df['speed_profile'] == speed_profile)
        ]
        return not matches.empty
    
    def generate_missing_configs(self) -> pd.DataFrame:
        """
        生成缺失的配置
        
        返回:
            包含缺失配置的DataFrame
        """
        if not self.missing_configs:
            print("没有缺失的配置需要生成")
            return pd.DataFrame()
        
        print(f"生成 {len(self.missing_configs)} 个缺失配置...")
        
        generated_configs = []
        
        for config in self.missing_configs:
            generated_config = self._generate_config(
                config['weight_class'],
                config['segment_type'],
                config['segment_length'],
                config['speed_profile']
            )
            generated_configs.append(generated_config)
        
        return pd.DataFrame(generated_configs)
    
    def _generate_config(self, weight_class: str, segment_type: str, length: float, speed_profile: int) -> Dict:
        """生成单个配置"""
        # 获取飞机参数
        aircraft_params = AIRCRAFT_PARAMETERS.get(weight_class, AIRCRAFT_PARAMETERS['Medium'])
        
        # 获取速度参数
        simplified_type = 'straight' if 'straight' in segment_type else 'turning'
        if simplified_type in SEGMENT_TYPE_SPEEDS:
            v0, v4, vmax = SEGMENT_TYPE_SPEEDS[simplified_type]
        else:
            v0, v4, vmax = SEGMENT_TYPE_SPEEDS['straight']
        
        if simplified_type == 'turning':
            # 转弯段：匀速通过
            a1, d1, d2, d4 = 0.0, 0.0, length, 0.0
            g1 = length / v0
            g2 = g1 * aircraft_params['fuel_flow_7']
        else:
            # 直线段：加速-匀速-减速
            a1 = 0.98
            d1 = min(length * 0.2, 20.0)
            d4 = min(length * 0.2, 20.0)
            d2 = max(0, length - d1 - d4)
            
            # 计算时间
            t1 = (vmax - v0) / a1 if a1 > 0 else 0
            t2 = d2 / vmax if vmax > 0 else 0
            t4 = (vmax - v4) / a1 if a1 > 0 else 0
            g1 = t1 + t2 + t4
            
            # 燃油消耗
            fuel_rate = aircraft_params['fuel_flow_30'] if vmax > 10 else aircraft_params['fuel_flow_7']
            g2 = g1 * fuel_rate
        
        return {
            'aircraft_weight_class': weight_class,
            'segment_type': segment_type,
            'segment_length': length,
            'speed_profile': speed_profile,
            'a1': a1,
            'd1': d1,
            'd2': d2,
            'd4': d4,
            'g1': g1,
            'g2': g2
        }
    
    def repair_config_database(self, output_file: str = None) -> pd.DataFrame:
        """
        修复配置数据库
        
        参数:
            output_file: 输出文件路径（可选）
            
        返回:
            修复后的完整配置DataFrame
        """
        print("开始修复配置数据库...")
        
        # 验证当前配置
        self.validate_completeness()
        
        if not self.missing_configs:
            print("配置数据库已完整，无需修复")
            return self.speed_profile_df
        
        # 生成缺失配置
        missing_df = self.generate_missing_configs()
        
        # 合并配置
        complete_df = pd.concat([self.speed_profile_df, missing_df], ignore_index=True)
        
        # 排序
        complete_df = complete_df.sort_values([
            'aircraft_weight_class', 'segment_type', 'segment_length', 'speed_profile'
        ]).reset_index(drop=True)
        
        print(f"修复完成:")
        print(f"原始配置: {len(self.speed_profile_df)}")
        print(f"新增配置: {len(missing_df)}")
        print(f"总配置: {len(complete_df)}")
        
        # 保存到文件
        if output_file:
            complete_df.to_csv(output_file, index=False)
            print(f"已保存到: {output_file}")
        
        return complete_df
    
    def analyze_length_distribution(self) -> Dict:
        """分析长度分布"""
        lengths = self.speed_profile_df['segment_length'].values
        
        analysis = {
            'count': len(lengths),
            'min': float(np.min(lengths)),
            'max': float(np.max(lengths)),
            'mean': float(np.mean(lengths)),
            'std': float(np.std(lengths)),
            'unique_count': len(np.unique(lengths)),
            'gaps': self._find_length_gaps(lengths)
        }
        
        print(f"\n长度分布分析:")
        print(f"总数: {analysis['count']}")
        print(f"范围: {analysis['min']:.1f} - {analysis['max']:.1f}米")
        print(f"平均: {analysis['mean']:.1f}米")
        print(f"标准差: {analysis['std']:.1f}米")
        print(f"唯一值: {analysis['unique_count']}")
        print(f"长度间隙: {len(analysis['gaps'])}个")
        
        return analysis
    
    def _find_length_gaps(self, lengths: np.ndarray, threshold: float = 5.0) -> List[Tuple[float, float]]:
        """找到长度间隙"""
        unique_lengths = np.unique(lengths)
        gaps = []
        
        for i in range(len(unique_lengths) - 1):
            gap = unique_lengths[i + 1] - unique_lengths[i]
            if gap > threshold:
                gaps.append((unique_lengths[i], unique_lengths[i + 1]))
        
        return gaps


def validate_and_repair_config(speed_profile_df: pd.DataFrame, repair: bool = True) -> pd.DataFrame:
    """
    验证和修复配置数据库的便捷函数
    
    参数:
        speed_profile_df: 原始速度配置数据
        repair: 是否进行修复
        
    返回:
        验证/修复后的配置数据
    """
    validator = ConfigValidator(speed_profile_df)
    
    # 分析长度分布
    validator.analyze_length_distribution()
    
    if repair:
        return validator.repair_config_database()
    else:
        validator.validate_completeness()
        return speed_profile_df


if __name__ == "__main__":
    # 示例用法
    print("配置验证工具示例")
    
    # 创建示例数据
    sample_data = []
    for weight in ['light', 'Medium']:
        for segment in ['straight', 'turning']:
            for length in [50, 100, 150]:
                sample_data.append({
                    'aircraft_weight_class': weight,
                    'segment_type': segment,
                    'segment_length': length,
                    'speed_profile': 1,
                    'a1': 0.98, 'd1': 10, 'd2': 80, 'd4': 10,
                    'g1': 25, 'g2': 0.6
                })
    
    sample_df = pd.DataFrame(sample_data)
    
    # 验证和修复
    complete_df = validate_and_repair_config(sample_df, repair=True)
    print(f"\n最终配置数量: {len(complete_df)}")
