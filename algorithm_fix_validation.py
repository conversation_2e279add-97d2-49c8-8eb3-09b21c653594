"""
AGM_NSGA2算法修复验证测试模块

本模块提供了验证修复效果的测试用例，确保所有修复都正确实施。
"""

import unittest
import numpy as np
import copy
from unittest.mock import Mock, patch

# 导入修复后的模块
from CalFitness_globalV2 import calculate_speed_profile_from_m2, repair_path_encoding_improved
from ES_global import constraint_aware_nsga2_selection
from deap import base, creator, tools


class TestM2ValueUsage(unittest.TestCase):
    """测试M2值使用逻辑修复"""

    def test_speed_profile_mapping(self):
        """测试M2值到speed_profile的映射（修正为两级配置）"""
        # 测试保守/经济模式范围
        self.assertEqual(calculate_speed_profile_from_m2(0.0), 1)
        self.assertEqual(calculate_speed_profile_from_m2(0.25), 1)
        self.assertEqual(calculate_speed_profile_from_m2(0.49), 1)

        # 测试激进/时间模式范围
        self.assertEqual(calculate_speed_profile_from_m2(0.5), 2)
        self.assertEqual(calculate_speed_profile_from_m2(0.75), 2)
        self.assertEqual(calculate_speed_profile_from_m2(1.0), 2)

    def test_speed_profile_boundary_values(self):
        """测试边界值（修正为两级配置）"""
        # 测试边界值0.5
        self.assertEqual(calculate_speed_profile_from_m2(0.49999), 1)
        self.assertEqual(calculate_speed_profile_from_m2(0.5), 2)

    def test_speed_profile_coverage(self):
        """测试所有可能的speed_profile值都能被生成（修正为两级配置）"""
        test_values = [0.25, 0.75]  # 代表两种模式
        profiles = [calculate_speed_profile_from_m2(v) for v in test_values]
        self.assertEqual(set(profiles), {1, 2})  # 只有1和2两种配置


class TestConstraintAwareSelection(unittest.TestCase):
    """测试约束感知的NSGA-II选择"""

    def setUp(self):
        """设置测试环境"""
        # 创建DEAP适应度和个体类型
        try:
            creator.create("FitnessMulti", base.Fitness, weights=(-1.0, -1.0))
            creator.create("Individual", list, fitness=creator.FitnessMulti)
        except RuntimeError:
            pass

    def create_test_individual(self, fitness_values, constraints):
        """创建测试个体"""
        individual = creator.Individual([1, 2, 3])
        individual.fitness.values = fitness_values
        individual.constraints = constraints
        return individual

    def test_all_feasible_selection(self):
        """测试全部可行解的选择"""
        individuals = [
            self.create_test_individual((1.0, 2.0), 0),
            self.create_test_individual((2.0, 1.0), 0),
            self.create_test_individual((1.5, 1.5), 0),
        ]

        selected = constraint_aware_nsga2_selection(individuals, 2)
        self.assertEqual(len(selected), 2)
        # 所有选择的个体都应该是可行的
        for ind in selected:
            self.assertEqual(ind.constraints, 0)

    def test_mixed_feasible_infeasible_selection(self):
        """测试可行解和不可行解混合的选择"""
        individuals = [
            self.create_test_individual((1.0, 2.0), 0),  # 可行
            self.create_test_individual((2.0, 1.0), 1),  # 不可行，约束违反=1
            self.create_test_individual((1.5, 1.5), 2),  # 不可行，约束违反=2
        ]

        selected = constraint_aware_nsga2_selection(individuals, 2)
        self.assertEqual(len(selected), 2)

        # 第一个应该是可行解
        self.assertEqual(selected[0].constraints, 0)
        # 第二个应该是约束违反最小的不可行解
        self.assertEqual(selected[1].constraints, 1)

    def test_all_infeasible_selection(self):
        """测试全部不可行解的选择"""
        individuals = [
            self.create_test_individual((1.0, 2.0), 3),
            self.create_test_individual((2.0, 1.0), 1),
            self.create_test_individual((1.5, 1.5), 2),
        ]

        selected = constraint_aware_nsga2_selection(individuals, 2)
        self.assertEqual(len(selected), 2)

        # 应该按约束违反程度排序选择
        self.assertEqual(selected[0].constraints, 1)
        self.assertEqual(selected[1].constraints, 2)


class TestPathEncodingRepair(unittest.TestCase):
    """测试路径编码修复策略"""

    def test_repair_maintains_relative_order(self):
        """测试修复保持M1值的相对顺序"""
        # 创建测试数据
        evaluate_individual = [0, (5.0, 0.5), (3.0, 0.3), (7.0, 0.8)]
        path_with_m2 = [('A', 0.5), ('B', 0.3), ('C', 0.8)]
        node_vector = ['A', 'B', 'C']
        min_path_length = {'A': 2, 'B': 1, 'C': 3}
        minHop = 3

        # 执行修复
        repaired = repair_path_encoding_improved(
            evaluate_individual, path_with_m2, node_vector, min_path_length, minHop
        )

        # 检查相对顺序是否保持
        m1_values = [repaired[i][0] for i in range(1, len(repaired))]
        original_order = [5.0, 3.0, 7.0]

        # 相对顺序应该保持：7.0 > 5.0 > 3.0
        self.assertGreater(m1_values[2], m1_values[0])  # C > A
        self.assertGreater(m1_values[0], m1_values[1])  # A > B

    def test_repair_ensures_positive_values(self):
        """测试修复确保所有M1值为正"""
        evaluate_individual = [0, (-2.0, 0.5), (-5.0, 0.3), (1.0, 0.8)]
        path_with_m2 = [('A', 0.5), ('B', 0.3), ('C', 0.8)]
        node_vector = ['A', 'B', 'C']
        min_path_length = {'A': 2, 'B': 1, 'C': 3}
        minHop = 3

        repaired = repair_path_encoding_improved(
            evaluate_individual, path_with_m2, node_vector, min_path_length, minHop
        )

        # 所有M1值都应该为正
        for i in range(1, len(repaired)):
            self.assertGreater(repaired[i][0], 0)

    def test_repair_preserves_m2_values(self):
        """测试修复保持M2值不变"""
        evaluate_individual = [0, (5.0, 0.5), (3.0, 0.3), (7.0, 0.8)]
        path_with_m2 = [('A', 0.5), ('B', 0.3), ('C', 0.8)]
        node_vector = ['A', 'B', 'C']
        min_path_length = {'A': 2, 'B': 1, 'C': 3}
        minHop = 3

        repaired = repair_path_encoding_improved(
            evaluate_individual, path_with_m2, node_vector, min_path_length, minHop
        )

        # M2值应该保持不变
        for i in range(1, len(repaired)):
            self.assertEqual(repaired[i][1], evaluate_individual[i][1])


class TestAlgorithmIntegration(unittest.TestCase):
    """测试算法整体集成"""

    def test_m2_value_integration(self):
        """测试M2值在整个流程中的集成（修正为两级配置）"""
        # 模拟一个完整的M2值使用流程
        m2_value = 0.75
        speed_profile = calculate_speed_profile_from_m2(m2_value)

        # 验证M2值被正确转换为speed_profile（修正：0.75应该映射到2）
        self.assertEqual(speed_profile, 2)

        # 验证speed_profile在合理范围内（修正：只有1和2）
        self.assertIn(speed_profile, [1, 2])

    def test_constraint_handling_integration(self):
        """测试约束处理的集成"""
        # 创建混合种群
        try:
            creator.create("FitnessMulti", base.Fitness, weights=(-1.0, -1.0))
            creator.create("Individual", list, fitness=creator.FitnessMulti)
        except RuntimeError:
            pass

        individuals = []
        for i in range(5):
            ind = creator.Individual([i])
            ind.fitness.values = (float(i), float(5-i))
            ind.constraints = i % 2  # 交替可行/不可行
            individuals.append(ind)

        selected = constraint_aware_nsga2_selection(individuals, 3)

        # 验证选择结果
        self.assertEqual(len(selected), 3)

        # 可行解应该优先被选择
        feasible_count = sum(1 for ind in selected if ind.constraints == 0)
        total_feasible = sum(1 for ind in individuals if ind.constraints == 0)

        # 如果可行解足够，应该优先选择可行解
        if total_feasible >= 3:
            self.assertEqual(feasible_count, 3)


def run_validation_tests():
    """运行所有验证测试"""
    print("开始运行AGM_NSGA2算法修复验证测试...")

    # 创建测试套件
    test_suite = unittest.TestSuite()

    # 添加测试用例
    test_suite.addTest(unittest.makeSuite(TestM2ValueUsage))
    test_suite.addTest(unittest.makeSuite(TestConstraintAwareSelection))
    test_suite.addTest(unittest.makeSuite(TestPathEncodingRepair))
    test_suite.addTest(unittest.makeSuite(TestAlgorithmIntegration))

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过！算法修复验证成功。")
    else:
        print(f"\n❌ 测试失败：{len(result.failures)} 个失败，{len(result.errors)} 个错误")

    return result.wasSuccessful()


if __name__ == "__main__":
    run_validation_tests()
