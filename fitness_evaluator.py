"""
适应度评估器模块 - 计算路径的适应度值

本模块包含适应度评估的核心功能：
1. 段级别的适应度计算
2. 燃油消耗和时间计算
3. 约束处理和惩罚计算
4. 并行种群评估
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Dict, Any, Optional
from multiprocessing import Pool, cpu_count
from functools import partial
import copy

from data_structures import (
    PathSegment, EvaluationResult, PopulationEvaluationResult,
    SegmentEvaluationParams, PhysicsParams
)
from config import AIRCRAFT_PARAMETERS, SEGMENT_TYPE_SPEEDS, PARALLEL_CONFIG
from path_processor import PathProcessor
from segment_evaluator import SegmentEvaluator
from time_window_calculator import TimeWindowCalculator


class FitnessEvaluator:
    """适应度评估器类"""

    def __init__(self, speed_profile_df: pd.DataFrame, nodes_df: pd.DataFrame, edges_df: pd.DataFrame):
        """
        初始化适应度评估器

        参数:
            speed_profile_df: 速度配置数据
            nodes_df: 节点数据
            edges_df: 边数据
        """
        self.speed_profile_df = speed_profile_df
        self.nodes_df = nodes_df
        self.edges_df = edges_df

        # 初始化组件
        self.path_processor = PathProcessor(nodes_df, edges_df)
        self.segment_evaluator = SegmentEvaluator(edges_df)
        self.time_calculator = TimeWindowCalculator()

        # 缓存物理参数以提高性能
        self._physics_cache = {}

    def get_physics_params(self, weight_type: str, segment_type: str, segment_length: float,
                          speed_profile: int = 1) -> PhysicsParams:
        """
        获取物理参数，使用缓存提高性能

        参数:
            weight_type: 飞机重量类型
            segment_type: 段类型
            segment_length: 段长度
            speed_profile: 速度配置

        返回:
            物理参数对象
        """
        cache_key = SegmentEvaluationParams(segment_type, segment_length, weight_type, speed_profile)

        if cache_key in self._physics_cache:
            return self._physics_cache[cache_key]

        # 查找匹配的配置（现在包含容错逻辑）
        segment_info = self._find_segment_config(weight_type, segment_type, segment_length, speed_profile)

        # 新的容错逻辑确保总是能找到配置，所以不需要检查empty
        if segment_info.empty:
            # 这种情况理论上不应该发生，但作为最后的安全网
            raise ValueError(f"Critical error: No configuration found for {weight_type}, {segment_type}, {segment_length}")

        # 获取速度参数（使用简化的段类型）
        simplified_type = self._simplify_segment_type(segment_type)
        if simplified_type in SEGMENT_TYPE_SPEEDS:
            v0, v4, vmax = SEGMENT_TYPE_SPEEDS[simplified_type]
        else:
            v0, v4, vmax = SEGMENT_TYPE_SPEEDS['straight']  # 默认使用直线段参数

        physics_params = PhysicsParams(
            a1=segment_info['a1'].values[0],
            d1=segment_info['d1'].values[0],
            d2=segment_info['d2'].values[0],
            d4=segment_info['d4'].values[0],
            g1=segment_info['g1'].values[0],
            g2=segment_info['g2'].values[0],
            v0=v0,
            v4=v4
        )

        # 缓存结果
        self._physics_cache[cache_key] = physics_params
        return physics_params

    def _find_segment_config(self, weight_type: str, segment_type: str, segment_length: float,
                           speed_profile: int) -> pd.DataFrame:
        """
        查找段配置，支持多级回退策略

        参数:
            weight_type: 飞机重量类型
            segment_type: 段类型
            segment_length: 段长度
            speed_profile: 速度配置

        返回:
            匹配的配置DataFrame
        """
        # 策略1: 精确匹配（容忍度0.01）
        segment_info = self.speed_profile_df[
            (self.speed_profile_df['aircraft_weight_class'] == weight_type) &
            (self.speed_profile_df['segment_type'] == segment_type) &
            (np.isclose(self.speed_profile_df['segment_length'], segment_length, atol=0.01)) &
            (self.speed_profile_df['speed_profile'] == speed_profile)
        ]

        if not segment_info.empty:
            return segment_info

        # 策略2: 增加容忍度到1.0米
        segment_info = self.speed_profile_df[
            (self.speed_profile_df['aircraft_weight_class'] == weight_type) &
            (self.speed_profile_df['segment_type'] == segment_type) &
            (np.isclose(self.speed_profile_df['segment_length'], segment_length, atol=1.0)) &
            (self.speed_profile_df['speed_profile'] == speed_profile)
        ]

        if not segment_info.empty:
            return segment_info

        # 策略3: 找最接近的长度
        same_type_configs = self.speed_profile_df[
            (self.speed_profile_df['aircraft_weight_class'] == weight_type) &
            (self.speed_profile_df['segment_type'] == segment_type) &
            (self.speed_profile_df['speed_profile'] == speed_profile)
        ]

        if not same_type_configs.empty:
            # 找到最接近的长度
            length_diff = np.abs(same_type_configs['segment_length'] - segment_length)
            closest_idx = length_diff.idxmin()
            return same_type_configs.loc[[closest_idx]]

        # 策略4: 简化段类型（去掉特殊标记）
        simplified_type = self._simplify_segment_type(segment_type)
        if simplified_type != segment_type:
            segment_info = self.speed_profile_df[
                (self.speed_profile_df['aircraft_weight_class'] == weight_type) &
                (self.speed_profile_df['segment_type'] == simplified_type) &
                (np.isclose(self.speed_profile_df['segment_length'], segment_length, atol=1.0)) &
                (self.speed_profile_df['speed_profile'] == speed_profile)
            ]

            if not segment_info.empty:
                return segment_info

        # 策略5: 使用默认配置
        return self._get_default_config(weight_type, segment_type, segment_length, speed_profile)

    def _simplify_segment_type(self, segment_type: str) -> str:
        """简化段类型，去掉特殊标记"""
        if 'straight' in segment_type:
            return 'straight'
        elif 'turning' in segment_type:
            return 'turning'
        else:
            return segment_type

    def _get_default_config(self, weight_type: str, segment_type: str, segment_length: float, speed_profile: int) -> pd.DataFrame:
        """生成默认配置"""
        # 基于物理模型生成合理的默认值
        simplified_type = self._simplify_segment_type(segment_type)

        # 获取速度参数
        if simplified_type in SEGMENT_TYPE_SPEEDS:
            v0, v4, vmax = SEGMENT_TYPE_SPEEDS[simplified_type]
        else:
            v0, v4, vmax = SEGMENT_TYPE_SPEEDS['straight']  # 默认使用直线段参数

        # 基于飞机类型和段长度计算物理参数
        aircraft_params = AIRCRAFT_PARAMETERS.get(weight_type, AIRCRAFT_PARAMETERS['Medium'])

        if simplified_type == 'turning':
            # 转弯段：匀速通过
            a1, d1, d2, d4 = 0.0, 0.0, segment_length, 0.0
            g1 = segment_length / v0  # 时间
            g2 = g1 * aircraft_params['fuel_flow_7']  # 燃油消耗
        else:
            # 直线段：加速-匀速-减速
            a1 = 0.98  # 标准加速度
            d1 = min(segment_length * 0.2, 20.0)  # 加速距离，不超过20米
            d4 = min(segment_length * 0.2, 20.0)  # 减速距离，不超过20米
            d2 = max(0, segment_length - d1 - d4)  # 匀速距离

            # 计算时间和燃油消耗
            t1 = (vmax - v0) / a1 if a1 > 0 else 0
            t2 = d2 / vmax if vmax > 0 else 0
            t4 = (vmax - v4) / a1 if a1 > 0 else 0
            g1 = t1 + t2 + t4

            # 燃油消耗（简化计算）
            fuel_rate = aircraft_params['fuel_flow_30'] if vmax > 10 else aircraft_params['fuel_flow_7']
            g2 = g1 * fuel_rate

        # 创建默认配置DataFrame
        default_config = pd.DataFrame([{
            'aircraft_weight_class': weight_type,
            'segment_type': segment_type,
            'segment_length': segment_length,
            'speed_profile': speed_profile,
            'a1': a1,
            'd1': d1,
            'd2': d2,
            'd4': d4,
            'g1': g1,
            'g2': g2
        }])

        return default_config

    def evaluate_segment(self, segment: PathSegment, weight_type: str, current_time: float) -> Tuple[float, float, List, float]:
        """
        评估单个段

        参数:
            segment: 路径段
            weight_type: 飞机重量类型
            current_time: 当前时间

        返回:
            (g1, g2, 边时间窗, 段结束时间)
        """
        if segment.segment_type == 'turning':
            # 转弯段：使用固定燃油流量
            fuel_flow_7 = AIRCRAFT_PARAMETERS[weight_type]['fuel_flow_7']
            constant_speed = SEGMENT_TYPE_SPEEDS['turning'][0]

            g1 = segment.length / constant_speed
            g2 = g1 * fuel_flow_7

            edge_time_windows = self.time_calculator.calculate_segment_time_windows(
                segment, {'a1': 0, 'd1': 0, 'd2': segment.length, 'v0': constant_speed, 'v4': constant_speed},
                current_time
            )
        else:
            # 直线段：使用速度配置
            physics_params = self.get_physics_params(weight_type, segment.segment_type, segment.length)

            g1 = physics_params.g1
            g2 = physics_params.g2

            edge_time_windows = self.time_calculator.calculate_segment_time_windows(
                segment, physics_params.__dict__, current_time
            )

        # 计算段结束时间
        end_time = edge_time_windows[-1].time_window.end_time if edge_time_windows else current_time

        return g1, g2, edge_time_windows, end_time

    def evaluate_aircraft_path(self, individual_encoding: List, aircraft: pd.Series, aircraft_subgraph,
                              node_vector: List[str], min_path_length: Dict[str, int],
                              max_cost: Dict[str, float], airport_graph) -> EvaluationResult:
        """
        评估单个飞机的路径

        参数:
            individual_encoding: 个体编码
            aircraft: 飞机信息
            aircraft_subgraph: 飞机子图
            node_vector: 节点向量
            min_path_length: 最短路径长度
            max_cost: 最大惩罚成本
            airport_graph: 机场图

        返回:
            评估结果
        """
        # 基本信息提取
        start_node = aircraft['Start Node']
        end_node = aircraft['End Node']
        start_time = aircraft['Start Time'] + individual_encoding[0]
        weight_class = aircraft['Weight Class']

        # 获取权重类型
        weight_type = self._get_weight_type(weight_class)

        # 路径修复和解码
        repaired_encoding, path_with_m2, min_hop = self.path_processor.repair_path(
            individual_encoding, aircraft_subgraph, node_vector, start_node, end_node, min_path_length
        )

        # 边分类
        path_edges = self.path_processor.classify_edges(path_with_m2)

        # 处理终端边
        path_edges, path_with_m2, additional_hops = self.path_processor.process_terminal_edges(
            path_edges, path_with_m2, end_node
        )
        min_hop += additional_hops

        # 段合并
        segments = self.segment_evaluator.merge_segments(path_edges, path_with_m2, start_node, end_node)

        # 计算适应度
        total_g1, total_g2, all_edge_time_windows = self._calculate_segment_fitness(
            segments, weight_type, start_time
        )

        # 冲突检测
        conflicts, conflict_infos = self.time_calculator.calculate_time_window_conflicts(
            airport_graph, all_edge_time_windows
        )

        # 约束计算
        constraint1 = 1 if min_hop > 0 else 0
        constraint2 = 1 if conflicts > 0 else 0

        # 惩罚计算
        if constraint1:
            total_g1 += max_cost['max_time'] * min_hop
            total_g2 += max_cost['max_fuel'] * min_hop

        return EvaluationResult(
            aircraft_id=aircraft.name,
            total_g1=total_g1,
            total_g2=total_g2,
            edge_time_windows=all_edge_time_windows,
            constraint1=constraint1,
            constraint2=constraint2,
            conflicts=conflict_infos,
            repaired_individual=repaired_encoding
        )

    def _get_weight_type(self, weight_class: str) -> str:
        """获取权重类型"""
        mapping = {'1': 'light', '2': 'Medium', '3': 'Heavy'}
        if weight_class not in mapping:
            raise ValueError(f"Invalid weight_class: {weight_class}")
        return mapping[weight_class]

    def _calculate_segment_fitness(self, segments: List[PathSegment], weight_type: str,
                                  start_time: float) -> Tuple[float, float, List]:
        """计算所有段的适应度"""
        total_g1 = 0
        total_g2 = 0
        all_edge_time_windows = []
        current_time = start_time

        for segment in segments:
            g1, g2, edge_time_windows, end_time = self.evaluate_segment(segment, weight_type, current_time)

            total_g1 += g1
            total_g2 += g2
            all_edge_time_windows.extend(edge_time_windows)
            current_time = end_time

        return total_g1, total_g2, all_edge_time_windows

    def evaluate_population_parallel(self, population: List[Dict], aircraft_df, aircraft_subgraphs,
                                   node_vectors, min_path_lengths, max_cost, airport_graph) -> PopulationEvaluationResult:
        """
        并行评估整个种群

        参数:
            population: 种群列表
            aircraft_df: 飞机数据
            aircraft_subgraphs: 飞机子图字典
            node_vectors: 节点向量字典
            min_path_lengths: 最短路径长度字典
            max_cost: 最大惩罚成本
            airport_graph: 机场图

        返回:
            种群评估结果
        """
        if not PARALLEL_CONFIG['enable_parallel'] or len(population) <= PARALLEL_CONFIG['chunk_size']:
            return self.evaluate_population_sequential(
                population, aircraft_df, aircraft_subgraphs, node_vectors,
                min_path_lengths, max_cost, airport_graph
            )

        # 并行评估
        max_workers = PARALLEL_CONFIG['max_workers'] or min(cpu_count(), len(population))
        chunk_size = max(1, len(population) // max_workers)

        # 分割种群
        population_chunks = [population[i:i + chunk_size] for i in range(0, len(population), chunk_size)]

        # 创建评估函数的部分应用
        eval_func = partial(
            self._evaluate_chunk,
            aircraft_df=aircraft_df,
            aircraft_subgraphs=aircraft_subgraphs,
            node_vectors=node_vectors,
            min_path_lengths=min_path_lengths,
            max_cost=max_cost,
            airport_graph=copy.deepcopy(airport_graph)
        )

        # 并行处理
        with Pool(processes=max_workers) as pool:
            chunk_results = pool.map(eval_func, population_chunks)

        # 合并结果
        all_evaluated = []
        all_repaired = []
        all_details = []

        for chunk_eval, chunk_repaired, chunk_details in chunk_results:
            all_evaluated.extend(chunk_eval)
            all_repaired.extend(chunk_repaired)
            all_details.extend(chunk_details)

        return PopulationEvaluationResult(
            evaluated_population=all_evaluated,
            repaired_population=all_repaired,
            evaluation_details=all_details
        )

    def evaluate_population_sequential(self, population: List[Dict], aircraft_df, aircraft_subgraphs,
                                     node_vectors, min_path_lengths, max_cost, airport_graph) -> PopulationEvaluationResult:
        """
        串行评估整个种群
        """
        evaluated_population = []
        repaired_population = []
        evaluation_details = []

        for individual in population:
            individual_fitness = []
            individual_repaired = {}
            individual_details = []

            # 为每个个体创建独立的图副本
            individual_graph = copy.deepcopy(airport_graph)

            for aircraft_id, aircraft in aircraft_df.iterrows():
                aircraft_subgraph = aircraft_subgraphs[aircraft_id]
                node_vector = node_vectors[aircraft_id]
                min_path_length = {
                    node: min_path_lengths[(aircraft_id, node)]
                    for node in node_vectors[aircraft_id]
                }

                # 评估单个飞机
                result = self.evaluate_aircraft_path(
                    individual[aircraft_id], aircraft, aircraft_subgraph,
                    node_vector, min_path_length, max_cost, individual_graph
                )

                individual_fitness.append((result.total_g1, result.total_g2, result.get_total_constraints()))
                individual_repaired[aircraft_id] = result.repaired_individual
                individual_details.append(result)

                # 更新图的时间窗
                self.time_calculator.update_graph_time_windows(individual_graph, result.edge_time_windows)

            # 计算总适应度
            total_g1 = sum(fitness[0] for fitness in individual_fitness)
            total_g2 = sum(fitness[1] for fitness in individual_fitness)
            total_constraints = sum(fitness[2] for fitness in individual_fitness)

            evaluated_population.append((total_g1, total_g2, total_constraints))
            repaired_population.append(individual_repaired)
            evaluation_details.extend(individual_details)

        return PopulationEvaluationResult(
            evaluated_population=evaluated_population,
            repaired_population=repaired_population,
            evaluation_details=evaluation_details
        )

    def _evaluate_chunk(self, population_chunk: List[Dict], aircraft_df, aircraft_subgraphs,
                       node_vectors, min_path_lengths, max_cost, airport_graph) -> Tuple[List, List, List]:
        """评估种群块（用于并行处理）"""
        chunk_evaluated = []
        chunk_repaired = []
        chunk_details = []

        for individual in population_chunk:
            individual_fitness = []
            individual_repaired = {}
            individual_details = []

            # 为每个个体创建独立的图副本
            individual_graph = copy.deepcopy(airport_graph)

            for aircraft_id, aircraft in aircraft_df.iterrows():
                aircraft_subgraph = aircraft_subgraphs[aircraft_id]
                node_vector = node_vectors[aircraft_id]
                min_path_length = {
                    node: min_path_lengths[(aircraft_id, node)]
                    for node in node_vectors[aircraft_id]
                }

                # 评估单个飞机
                result = self.evaluate_aircraft_path(
                    individual[aircraft_id], aircraft, aircraft_subgraph,
                    node_vector, min_path_length, max_cost, individual_graph
                )

                individual_fitness.append((result.total_g1, result.total_g2, result.get_total_constraints()))
                individual_repaired[aircraft_id] = result.repaired_individual
                individual_details.append(result)

                # 更新图的时间窗
                self.time_calculator.update_graph_time_windows(individual_graph, result.edge_time_windows)

            # 计算总适应度
            total_g1 = sum(fitness[0] for fitness in individual_fitness)
            total_g2 = sum(fitness[1] for fitness in individual_fitness)
            total_constraints = sum(fitness[2] for fitness in individual_fitness)

            chunk_evaluated.append((total_g1, total_g2, total_constraints))
            chunk_repaired.append(individual_repaired)
            chunk_details.extend(individual_details)

        return chunk_evaluated, chunk_repaired, chunk_details
