"""
测试多进程错误修复

本脚本用于测试修复后的代码是否能正确处理缺失配置的情况
"""

import pandas as pd
import numpy as np
import sys
import traceback
from config import PARALLEL_CONFIG

def create_test_data():
    """创建测试数据，包含可能导致错误的配置"""
    
    # 创建节点数据
    nodes_data = []
    for i in range(10):
        nodes_data.append({
            'Node ID': f'N{i}',
            'X': i * 100,
            'Y': 0
        })
    nodes_df = pd.DataFrame(nodes_data)
    
    # 创建边数据
    edges_data = []
    for i in range(9):
        edges_data.append({
            'Start Node': f'N{i}',
            'End Node': f'N{i+1}',
            'Length': np.random.uniform(50, 100)
        })
    edges_df = pd.DataFrame(edges_data)
    
    # 创建不完整的速度配置数据（故意缺少某些组合）
    speed_data = []
    for weight_class in ['light', 'Medium']:  # 故意不包含Heavy
        for segment_type in ['straight', 'turning']:  # 故意不包含straight holding
            for length in [50, 100]:  # 故意不包含其他长度
                speed_data.append({
                    'aircraft_weight_class': weight_class,
                    'segment_type': segment_type,
                    'segment_length': length,
                    'speed_profile': 1,
                    'a1': 0.98,
                    'd1': 10,
                    'd2': 30,
                    'd4': 10,
                    'g1': 25,
                    'g2': 0.6
                })
    speed_df = pd.DataFrame(speed_data)
    
    # 创建飞机数据
    aircraft_data = []
    for i in range(3):
        aircraft_data.append({
            'Start Node': f'N{i}',
            'End Node': f'N{i+5}',
            'Start Time': i * 10,
            'Weight Class': str((i % 3) + 1)  # 1, 2, 3
        })
    aircraft_df = pd.DataFrame(aircraft_data)
    aircraft_df.index = range(len(aircraft_df))
    
    return nodes_df, edges_df, speed_df, aircraft_df

def test_missing_config_handling():
    """测试缺失配置的处理"""
    print("=== 测试缺失配置处理 ===")
    
    try:
        from CalFitness_globalV2 import evaluate_path
        
        # 创建测试数据
        nodes_df, edges_df, speed_df, aircraft_df = create_test_data()
        
        # 创建一个会导致缺失配置的测试用例
        aircraft = aircraft_df.iloc[0]  # 第一个飞机
        
        # 模拟个体编码
        individual_encoding = [5.0]  # 时间偏移
        for _ in range(10):  # 10个节点的编码
            individual_encoding.append((np.random.uniform(-1, 1), np.random.uniform(0, 1)))
        
        # 模拟其他必要参数
        aircraft_subG = None  # 简化测试
        node_vector = [f'N{i}' for i in range(10)]
        min_path_length = {f'N{i}': 0 for i in range(10)}
        maxCost = {'max_time': 1000, 'max_fuel': 100}
        
        # 创建简单的图
        import networkx as nx
        G1 = nx.Graph()
        for i in range(9):
            G1.add_edge(f'N{i}', f'N{i+1}')
        
        print("测试参数准备完成")
        print(f"速度配置数据库大小: {len(speed_df)}")
        print(f"飞机信息: {aircraft.to_dict()}")
        
        # 尝试评估路径（这里可能会触发缺失配置的情况）
        try:
            result = evaluate_path(
                individual_encoding, aircraft, aircraft_subG, node_vector,
                min_path_length, nodes_df, edges_df, speed_df, maxCost, G1
            )
            print("✓ 路径评估成功完成")
            print(f"结果: g1={result[0]:.2f}, g2={result[1]:.2f}, 约束={result[3]+result[4]}")
            return True
            
        except Exception as e:
            print(f"✗ 路径评估失败: {e}")
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"✗ 测试设置失败: {e}")
        traceback.print_exc()
        return False

def test_parallel_evaluation():
    """测试并行评估"""
    print("\n=== 测试并行评估 ===")
    
    try:
        from CalFitness_globalV2 import evaluate_population
        
        # 创建测试数据
        nodes_df, edges_df, speed_df, aircraft_df = create_test_data()
        
        # 创建小规模种群
        population = []
        for _ in range(5):  # 5个个体
            individual = {}
            for aircraft_id in range(len(aircraft_df)):
                encoding = [np.random.uniform(0, 10)]  # 时间偏移
                for _ in range(10):  # 10个节点
                    encoding.append((np.random.uniform(-1, 1), np.random.uniform(0, 1)))
                individual[aircraft_id] = encoding
            population.append(individual)
        
        # 模拟其他参数
        aircraft_subgraphs = {i: None for i in range(len(aircraft_df))}
        node_vectors = {i: [f'N{j}' for j in range(10)] for i in range(len(aircraft_df))}
        min_path_lengths = {(i, f'N{j}'): 0 for i in range(len(aircraft_df)) for j in range(10)}
        maxCost = {'max_time': 1000, 'max_fuel': 100}
        
        # 创建图
        import networkx as nx
        G = nx.Graph()
        for i in range(9):
            G.add_edge(f'N{i}', f'N{i+1}')
        
        print(f"测试种群大小: {len(population)}")
        print(f"飞机数量: {len(aircraft_df)}")
        
        # 测试串行评估
        print("\n测试串行评估...")
        PARALLEL_CONFIG['enable_parallel'] = False
        
        try:
            eval_pop, repair_pop = evaluate_population(
                population, aircraft_df, aircraft_subgraphs, node_vectors,
                min_path_lengths, nodes_df, edges_df, speed_df, maxCost, G
            )
            print(f"✓ 串行评估成功，结果数量: {len(eval_pop)}")
        except Exception as e:
            print(f"✗ 串行评估失败: {e}")
            traceback.print_exc()
            return False
        
        # 测试并行评估
        print("\n测试并行评估...")
        PARALLEL_CONFIG['enable_parallel'] = True
        PARALLEL_CONFIG['chunk_size'] = 2
        
        try:
            eval_pop_parallel, repair_pop_parallel = evaluate_population(
                population, aircraft_df, aircraft_subgraphs, node_vectors,
                min_path_lengths, nodes_df, edges_df, speed_df, maxCost, G
            )
            print(f"✓ 并行评估成功，结果数量: {len(eval_pop_parallel)}")
            return True
        except Exception as e:
            print(f"✗ 并行评估失败: {e}")
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"✗ 并行测试设置失败: {e}")
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    test_cases = [
        {
            'name': '缺失Heavy类型配置',
            'weight_class': '3',  # Heavy
            'segment_type': 'straight holding',
            'segment_length': 86.286  # 原始错误中的长度
        },
        {
            'name': '缺失特殊段类型',
            'weight_class': '2',  # Medium
            'segment_type': 'straight breakaway',
            'segment_length': 75.5
        },
        {
            'name': '非标准长度',
            'weight_class': '1',  # light
            'segment_type': 'turning',
            'segment_length': 123.456
        }
    ]
    
    success_count = 0
    
    for test_case in test_cases:
        print(f"\n测试: {test_case['name']}")
        
        try:
            # 这里我们直接测试容错逻辑
            from config import AIRCRAFT_PARAMETERS
            
            weight_type_map = {'1': 'light', '2': 'Medium', '3': 'Heavy'}
            weight_type = weight_type_map[test_case['weight_class']]
            
            # 检查飞机参数是否存在
            if weight_type in AIRCRAFT_PARAMETERS:
                print(f"✓ 飞机类型 {weight_type} 参数可用")
            else:
                print(f"✗ 飞机类型 {weight_type} 参数缺失")
                continue
            
            # 模拟默认配置生成
            aircraft_params = AIRCRAFT_PARAMETERS[weight_type]
            segment_type = test_case['segment_type']
            segment_length = test_case['segment_length']
            
            if 'turning' in segment_type:
                g1 = segment_length / 5.14
                g2 = g1 * aircraft_params['fuel_flow_7']
            else:
                # 简化的直线段计算
                g1 = segment_length / 5.14 * 1.5  # 考虑加减速
                g2 = g1 * aircraft_params['fuel_flow_30']
            
            print(f"✓ 默认配置生成成功: g1={g1:.2f}, g2={g2:.4f}")
            success_count += 1
            
        except Exception as e:
            print(f"✗ 测试失败: {e}")
    
    print(f"\n边界测试结果: {success_count}/{len(test_cases)} 通过")
    return success_count == len(test_cases)

def main():
    """主测试函数"""
    print("开始多进程错误修复测试...")
    
    tests = [
        ("缺失配置处理", test_missing_config_handling),
        ("并行评估", test_parallel_evaluation),
        ("边界情况", test_edge_cases)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"运行测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            traceback.print_exc()
    
    print(f"\n{'='*50}")
    print(f"测试总结")
    print('='*50)
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！多进程错误已修复！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
