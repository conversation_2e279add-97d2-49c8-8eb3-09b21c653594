"""
段评估器模块 - 处理路径段的合并和分类

本模块包含路径段处理的核心功能：
1. 相同类型边的合并
2. 段类型的特殊标记（breakaway, holding）
3. 段信息的结构化存储
"""

from typing import List, Tuple, Dict, Any
from data_structures import PathSegment
import pandas as pd


class SegmentEvaluator:
    """段评估器类"""
    
    def __init__(self, edges_df: pd.DataFrame):
        """
        初始化段评估器
        
        参数:
            edges_df: 边数据DataFrame
        """
        self.edges_df = edges_df
    
    def merge_segments(self, path_edges: List[Tuple[str, float, str, str]], 
                      path_with_m2: List[Tuple[str, float]], 
                      start_node: str, end_node: str) -> List[PathSegment]:
        """
        将相同类型的边合并为段
        
        参数:
            path_edges: 边信息列表 [(edge_type, length, start_node, end_node), ...]
            path_with_m2: 路径节点信息 [(node, m2_value), ...]
            start_node: 起始节点
            end_node: 终止节点
            
        返回:
            路径段列表
        """
        if not path_edges:
            return []
        
        segments = []
        current_segment_type = path_edges[0][0]
        current_segment_length = 0
        current_segment_start = path_edges[0][2]
        current_segment_end = path_edges[0][3]
        current_segment_start_m2 = path_with_m2[0][1]
        current_segment_nodes = [current_segment_start]
        current_segment_lengths = []
        
        first_straight_segment = True
        
        for i, (edge_type, _, edge_start, edge_end) in enumerate(path_edges):
            # 获取边的实际长度
            edge_length = self._get_edge_length(edge_start, edge_end)
            
            if current_segment_type == edge_type:
                # 相同类型，合并到当前段
                current_segment_length += edge_length
                current_segment_end = edge_end
                current_segment_nodes.append(edge_end)
                current_segment_lengths.append(edge_length)
            else:
                # 不同类型，保存当前段并开始新段
                segment_type = self._classify_segment_type(
                    current_segment_type, 
                    current_segment_start, 
                    current_segment_end,
                    start_node, 
                    end_node, 
                    first_straight_segment
                )
                
                segments.append(PathSegment(
                    segment_type=segment_type,
                    length=current_segment_length,
                    start_node=current_segment_start,
                    end_node=current_segment_end,
                    start_m2=current_segment_start_m2,
                    nodes=current_segment_nodes.copy(),
                    edge_lengths=current_segment_lengths.copy()
                ))
                
                # 开始新段
                current_segment_type = edge_type
                current_segment_length = edge_length
                current_segment_start = edge_start
                current_segment_end = edge_end
                current_segment_start_m2 = path_with_m2[i][1]
                current_segment_nodes = [current_segment_start, current_segment_end]
                current_segment_lengths = [edge_length]
                
                if current_segment_type == 'straight':
                    first_straight_segment = False
        
        # 处理最后一个段
        segment_type = self._classify_segment_type(
            current_segment_type,
            current_segment_start,
            current_segment_end,
            start_node,
            end_node,
            first_straight_segment
        )
        
        segments.append(PathSegment(
            segment_type=segment_type,
            length=current_segment_length,
            start_node=current_segment_start,
            end_node=current_segment_end,
            start_m2=current_segment_start_m2,
            nodes=current_segment_nodes,
            edge_lengths=current_segment_lengths
        ))
        
        return segments
    
    def _get_edge_length(self, start_node: str, end_node: str) -> float:
        """
        获取边的长度
        
        参数:
            start_node: 起始节点
            end_node: 终止节点
            
        返回:
            边长度
        """
        edge_info = self.edges_df[
            ((self.edges_df['Start Node'] == start_node) & (self.edges_df['End Node'] == end_node)) |
            ((self.edges_df['Start Node'] == end_node) & (self.edges_df['End Node'] == start_node))
        ]
        
        if edge_info.empty:
            raise ValueError(f"No edge found between {start_node} and {end_node}")
        
        return edge_info['Length'].values[0]
    
    def _classify_segment_type(self, base_type: str, segment_start: str, segment_end: str,
                              path_start: str, path_end: str, is_first_straight: bool) -> str:
        """
        对段类型进行分类，添加特殊标记
        
        参数:
            base_type: 基础类型 ('straight' 或 'turning')
            segment_start: 段起始节点
            segment_end: 段终止节点
            path_start: 路径起始节点
            path_end: 路径终止节点
            is_first_straight: 是否是第一个直线段
            
        返回:
            分类后的段类型
        """
        if base_type == 'straight':
            # 第一个直线段且起点是路径起点，标记为 breakaway
            if is_first_straight and segment_start == path_start:
                return 'straight breakaway'
            # 最后一个直线段且终点是路径终点，标记为 holding
            elif segment_end == path_end:
                return 'straight holding'
            else:
                return 'straight'
        else:
            return base_type
    
    def create_legacy_segments(self, segments: List[PathSegment]) -> List[Tuple]:
        """
        创建与原始代码兼容的段格式
        
        参数:
            segments: PathSegment对象列表
            
        返回:
            原始格式的段列表
        """
        legacy_segments = []
        
        for segment in segments:
            legacy_segment = (
                segment.segment_type,
                round(segment.length, 2),
                segment.start_node,
                segment.end_node,
                segment.start_m2,
                segment.nodes,
                segment.edge_lengths
            )
            legacy_segments.append(legacy_segment)
        
        return legacy_segments
    
    def validate_segments(self, segments: List[PathSegment]) -> bool:
        """
        验证段的有效性
        
        参数:
            segments: 段列表
            
        返回:
            是否有效
        """
        if not segments:
            return True
        
        # 检查段的连续性
        for i in range(len(segments) - 1):
            if segments[i].end_node != segments[i + 1].start_node:
                return False
        
        # 检查每个段的内部一致性
        for segment in segments:
            if len(segment.nodes) != len(segment.edge_lengths) + 1:
                return False
            
            calculated_length = sum(segment.edge_lengths)
            if abs(calculated_length - segment.length) > 1e-6:
                return False
        
        return True
